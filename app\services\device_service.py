"""
设备信息管理服务

实现设备信息的提取、创建和关联等业务逻辑
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import uuid4, uuid5, NAMESPACE_DNS

from app.database.connection import Neo4jConnection
from app.repositories.base import BaseRepository
from app.models.alert import AlertDetailCreate, EvidenceArtifactsModel
from app.models.base import EntityType
from app.core.constants import NodeLabels, RelationshipTypes
from app.utils.logger import logger


class DeviceService:
    """设备信息管理服务"""
    
    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
        self.repository = BaseRepository(connection)
    
    def extract_and_create_device(self, alert_data: AlertDetailCreate) -> Optional[str]:
        """
        从告警细节中提取设备信息并创建设备节点
        返回设备ID，如果无法提取则返回None
        """
        device_info = self._extract_device_info(alert_data)
        
        if not device_info:
            return None
        
        # 检查设备是否已存在
        device_id = self._generate_device_id(device_info)
        existing_device = self._get_device_by_id(device_id)
        
        if existing_device:
            # 设备已存在，更新告警数量
            self._increment_alert_count(device_id)
            return device_id
        else:
            # 创建新设备
            return self._create_device_node(device_id, device_info)
    
    def link_alert_to_device(self, alert_id: str, device_id: str):
        """关联告警到设备"""
        if not device_id:
            return
            
        current_time = datetime.now()
        
        # 创建 GENERATED_BY 关系（告警由设备生成），只在失败时记录日志
        relationship_created = self.repository.create_relationship(
            alert_id,
            device_id,
            RelationshipTypes.GENERATED_BY,
            {
                "created_time": current_time.isoformat(),
                "relationship_reason": "告警由此设备生成"
            },
            "AlertDetail",  # from_label
            "Device"        # to_label
        )
        
        if not relationship_created:
            logger.error("告警-设备关系创建失败", alert_id=alert_id, device_id=device_id)
    
    def _extract_device_info(self, alert_data: AlertDetailCreate) -> Optional[Dict[str, Any]]:
        """从告警细节中提取设备信息"""
        device_info = {
            "product_name": alert_data.product_name,
            "uuid": None,
            "org": None,
            "ip": None,
            "hostname": None,
            "mac": None,
            "os_name": None
        }
        
        # 从 evidence_artifacts 中提取设备信息
        if alert_data.evidence_artifacts:
            for evidence in alert_data.evidence_artifacts:
                self._extract_from_evidence(evidence, device_info)
        
        # 从原始数据中尝试提取设备信息
        if alert_data.raw_data:
            self._extract_from_raw_data(alert_data.raw_data, device_info)
        
        # 验证是否有足够的设备信息
        if not any([device_info["ip"], device_info["hostname"], device_info["mac"], device_info["uuid"]]):
            # 如果没有关键的设备标识信息，使用product_name作为fallback
            if device_info["product_name"]:
                device_info["hostname"] = device_info["product_name"]
                return device_info
            return None
        
        return device_info
    
    def _extract_from_evidence(self, evidence: EvidenceArtifactsModel, device_info: Dict[str, Any]):
        """从证据中提取设备信息"""
        # 从源端点提取设备信息
        if evidence.src_endpoint:
            if evidence.src_endpoint.ip:
                device_info["ip"] = device_info["ip"] or evidence.src_endpoint.ip
            if evidence.src_endpoint.hostname:
                device_info["hostname"] = device_info["hostname"] or evidence.src_endpoint.hostname
            if evidence.src_endpoint.mac:
                device_info["mac"] = device_info["mac"] or evidence.src_endpoint.mac
            if evidence.src_endpoint.uid:
                device_info["uuid"] = device_info["uuid"] or evidence.src_endpoint.uid
            if evidence.src_endpoint.os and evidence.src_endpoint.os.name:
                device_info["os_name"] = device_info["os_name"] or evidence.src_endpoint.os.name
        
        # 从目标端点提取设备信息（如果源端点信息不完整）
        if evidence.dst_endpoint and not device_info["ip"]:
            if evidence.dst_endpoint.ip:
                device_info["ip"] = evidence.dst_endpoint.ip
            if evidence.dst_endpoint.hostname:
                device_info["hostname"] = device_info["hostname"] or evidence.dst_endpoint.hostname
            if evidence.dst_endpoint.mac:
                device_info["mac"] = device_info["mac"] or evidence.dst_endpoint.mac
            if evidence.dst_endpoint.uid:
                device_info["uuid"] = device_info["uuid"] or evidence.dst_endpoint.uid
            if evidence.dst_endpoint.os and evidence.dst_endpoint.os.name:
                device_info["os_name"] = device_info["os_name"] or evidence.dst_endpoint.os.name
        
        # 从进程信息中提取用户和系统信息
        if evidence.process:
            if evidence.process.user and evidence.process.user.name:
                # 可以将用户信息添加到设备属性中
                device_info["primary_user"] = evidence.process.user.name
        
        # 从HTTP请求中提取主机信息
        if evidence.http_request and evidence.http_request.url:
            if evidence.http_request.url.hostname and not device_info["hostname"]:
                device_info["hostname"] = evidence.http_request.url.hostname
    
    def _extract_from_raw_data(self, raw_data: str, device_info: Dict[str, Any]):
        """从原始数据中提取设备信息（简单的字符串匹配）"""
        try:
            import json
            data = json.loads(raw_data)
            
            # 尝试从常见字段中提取设备信息
            device_fields = {
                "uuid": ["device_id", "machine_id", "host_id", "endpoint_id"],
                "ip": ["src_ip", "source_ip", "host_ip", "device_ip"],
                "hostname": ["hostname", "host_name", "computer_name", "device_name"],
                "mac": ["mac_address", "mac", "hardware_address"],
                "os_name": ["os", "operating_system", "platform", "os_name"]
            }
            
            for device_key, possible_fields in device_fields.items():
                if not device_info[device_key]:  # 只在当前值为空时提取
                    for field in possible_fields:
                        if field in data and data[field]:
                            device_info[device_key] = str(data[field])
                            break
        
        except (json.JSONDecodeError, KeyError, TypeError):
            # 如果解析失败，忽略原始数据提取
            pass
    
    def _generate_device_id(self, device_info: Dict[str, Any]) -> str:
        """生成设备ID"""
        # 按优先级使用标识符生成ID
        identifiers = [
            device_info.get("uuid"),
            device_info.get("mac"),
            device_info.get("ip"),
            device_info.get("hostname"),
            device_info.get("product_name")
        ]
        
        # 取第一个非空的标识符
        primary_identifier = next((id for id in identifiers if id), "unknown_device")
        
        # 使用UUID5生成确定性ID
        namespace = uuid5(NAMESPACE_DNS, "device")
        return str(uuid5(namespace, f"device--{primary_identifier}"))
    
    def _get_device_by_id(self, device_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取设备"""
        query = """
        MATCH (d:Device {device_id: $device_id})
        RETURN d
        """
        
        result = self.connection.execute_read_transaction(query, {"device_id": device_id})
        
        if result:
            return dict(result[0]["d"])
        return None
    
    def _create_device_node(self, device_id: str, device_info: Dict[str, Any]) -> str:
        """创建设备节点"""
        current_time = datetime.now()
        
        properties = {
            "device_id": device_id,
            "uuid": device_info.get("uuid"),
            "org": device_info.get("org"),
            "ip": device_info.get("ip"),
            "hostname": device_info.get("hostname"),
            "mac": device_info.get("mac"),
            "os_name": device_info.get("os_name"),
            "product_name": device_info.get("product_name"),
            "primary_user": device_info.get("primary_user"),
            "count": 1,  # 初始告警数量为1
            "created_at": current_time.isoformat(),
            "updated_at": current_time.isoformat()
        }
        
        # 移除空值
        properties = {k: v for k, v in properties.items() if v is not None}
        
        self.repository.create_node(NodeLabels.DEVICE, properties)
        return device_id
    
    def _increment_alert_count(self, device_id: str):
        """增加设备的告警数量"""
        query = """
        MATCH (d:Device {device_id: $device_id})
        SET d.count = d.count + 1, d.updated_at = $updated_at
        """
        
        self.connection.execute_write_transaction(
            query,
            {
                "device_id": device_id,
                "updated_at": datetime.now().isoformat()
            }
        )
    
    def get_device_alerts(self, device_id: str) -> List[Dict[str, Any]]:
        """获取设备相关的告警列表"""
        query = """
        MATCH (d:Device {device_id: $device_id})<-[:GENERATED_BY]-(a:AlertDetail)
        RETURN a
        ORDER BY a.time DESC
        """
        
        result = self.connection.execute_read_transaction(query, {"device_id": device_id})
        return [dict(record["a"]) for record in result]
    
    def get_device_statistics(self, device_id: str) -> Dict[str, Any]:
        """获取设备统计信息"""
        query = """
        MATCH (d:Device {device_id: $device_id})
        OPTIONAL MATCH (d)<-[:GENERATED_BY]-(a:AlertDetail)
        RETURN d,
               count(a) as total_alerts,
               count(CASE WHEN a.status = 'New' THEN 1 END) as new_alerts,
               count(CASE WHEN a.severity = 'Critical' THEN 1 END) as critical_alerts,
               count(CASE WHEN a.severity = 'High' THEN 1 END) as high_alerts
        """
        
        result = self.connection.execute_read_transaction(query, {"device_id": device_id})
        
        if result:
            record = result[0]
            device_data = dict(record["d"])
            
            return {
                "device": device_data,
                "statistics": {
                    "total_alerts": record["total_alerts"],
                    "new_alerts": record["new_alerts"], 
                    "critical_alerts": record["critical_alerts"],
                    "high_alerts": record["high_alerts"]
                }
            }
        
        return None
    
    def search_devices(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """搜索设备"""
        where_conditions = []
        parameters = {}
        
        if filters.get("ip"):
            where_conditions.append("d.ip CONTAINS $ip")
            parameters["ip"] = filters["ip"]
        
        if filters.get("hostname"):
            where_conditions.append("toLower(d.hostname) CONTAINS toLower($hostname)")
            parameters["hostname"] = filters["hostname"]
        
        if filters.get("product_name"):
            where_conditions.append("toLower(d.product_name) CONTAINS toLower($product_name)")
            parameters["product_name"] = filters["product_name"]
        
        if filters.get("os_name"):
            where_conditions.append("toLower(d.os_name) CONTAINS toLower($os_name)")
            parameters["os_name"] = filters["os_name"]
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        query = f"""
        MATCH (d:Device)
        WHERE {where_clause}
        OPTIONAL MATCH (d)<-[:GENERATED_BY]-(a:AlertDetail)
        RETURN d, count(a) as alert_count
        ORDER BY alert_count DESC, d.updated_at DESC
        LIMIT 100
        """
        
        result = self.connection.execute_read_transaction(query, parameters)
        
        devices = []
        for record in result:
            device_data = dict(record["d"])
            device_data["alert_count"] = record["alert_count"]
            devices.append(device_data)
        
        return devices 