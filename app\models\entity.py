"""
实体相关数据模型

定义风险实体、关系等数据模型
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator

from .base import TimestampedModel, EntityType


class EntityBase(BaseModel):
    """实体基础模型"""
    entity_type: EntityType = Field(..., description="实体类型")
    name: str = Field(..., description="实体名称")
    properties: Dict[str, Any] = Field(..., description="实体属性")
    risk_score: int = Field(0, ge=0, le=100, description="风险分数(0-100)")
    description: Optional[str] = Field(None, description="实体描述")


class EntityCreate(EntityBase):
    """创建实体请求模型"""
    pass


class EntityUpdate(BaseModel):
    """更新实体请求模型"""
    name: Optional[str] = Field(None, description="实体名称")
    properties: Optional[Dict[str, Any]] = Field(None, description="要更新的实体属性")
    risk_score: Optional[int] = Field(None, ge=0, le=100, description="风险分数(0-100)")
    description: Optional[str] = Field(None, description="实体描述")


class EntityResponse(EntityBase, TimestampedModel):
    """实体响应模型"""
    entity_id: str = Field(..., description="实体ID")
    
    model_config = {"from_attributes": True}


class RelationshipBase(BaseModel):
    """关系基础模型"""
    relationship_type: str = Field(..., description="关系类型")
    properties: Optional[Dict[str, Any]] = Field(None, description="关系属性")


class RelationshipCreate(RelationshipBase):
    """创建关系请求模型"""
    from_entity_id: str = Field(..., description="起始实体ID")
    to_entity_id: str = Field(..., description="目标实体ID")


class RelationshipResponse(RelationshipBase, TimestampedModel):
    """关系响应模型"""
    relationship_id: str = Field(..., description="关系ID")
    from_entity_id: str = Field(..., description="起始实体ID")
    to_entity_id: str = Field(..., description="目标实体ID")
    
    model_config = {"from_attributes": True}


class EntitySearchParams(BaseModel):
    """实体搜索参数"""
    entity_type: Optional[EntityType] = Field(None, description="实体类型")
    properties: Optional[Dict[str, Any]] = Field(None, description="属性过滤条件")
    limit: int = Field(50, ge=1, le=1000, description="返回数量限制")


class PathResponse(BaseModel):
    """路径查询响应模型"""
    path_length: int = Field(..., description="路径长度")
    nodes: List[EntityResponse] = Field(..., description="路径节点列表")
    relationships: List[RelationshipResponse] = Field(..., description="路径关系列表")


# 具体实体类型模型

class HttpRequestEntity(BaseModel):
    """HTTP请求实体"""
    method: Optional[str] = Field(None, description="HTTP方法")
    url: Optional[str] = Field(None, description="请求URL")
    headers: Optional[Dict[str, str]] = Field(None, description="请求头")
    body: Optional[str] = Field(None, description="请求体")
    user_agent: Optional[str] = Field(None, description="User-Agent")
    source_ip: Optional[str] = Field(None, description="源IP地址")


class HttpResponseEntity(BaseModel):
    """HTTP响应实体"""
    status_code: Optional[int] = Field(None, description="响应状态码")
    headers: Optional[Dict[str, str]] = Field(None, description="响应头")
    body: Optional[str] = Field(None, description="响应体")
    content_type: Optional[str] = Field(None, description="内容类型")


class DnsQueryEntity(BaseModel):
    """DNS查询实体"""
    query_name: Optional[str] = Field(None, description="查询域名")
    query_type: Optional[str] = Field(None, description="查询类型")
    response_code: Optional[str] = Field(None, description="响应代码")
    answers: Optional[List[str]] = Field(None, description="查询结果")


class NetworkEndpointEntity(BaseModel):
    """网络端点实体"""
    ip: Optional[str] = Field(None, description="IP地址")
    port: Optional[int] = Field(None, description="端口号")
    hostname: Optional[str] = Field(None, description="主机名")
    mac: Optional[str] = Field(None, description="MAC地址")
    protocol: Optional[str] = Field(None, description="协议类型")


class UrlEntity(BaseModel):
    """URL实体"""
    url: str = Field(..., description="URL地址")
    domain: Optional[str] = Field(None, description="域名")
    path: Optional[str] = Field(None, description="路径")
    query_params: Optional[Dict[str, str]] = Field(None, description="查询参数")
    fragment: Optional[str] = Field(None, description="片段")


class FileEntity(BaseModel):
    """文件实体"""
    filename: Optional[str] = Field(None, description="文件名")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_size: Optional[int] = Field(None, description="文件大小")
    file_hash: Optional[str] = Field(None, description="文件哈希")
    mime_type: Optional[str] = Field(None, description="MIME类型")
    created_time: Optional[datetime] = Field(None, description="创建时间")
    modified_time: Optional[datetime] = Field(None, description="修改时间")


class ProcessEntity(BaseModel):
    """进程实体"""
    process_id: Optional[int] = Field(None, description="进程ID")
    process_name: Optional[str] = Field(None, description="进程名称")
    command_line: Optional[str] = Field(None, description="命令行")
    parent_process_id: Optional[int] = Field(None, description="父进程ID")
    user: Optional[str] = Field(None, description="执行用户")
    start_time: Optional[datetime] = Field(None, description="启动时间") 