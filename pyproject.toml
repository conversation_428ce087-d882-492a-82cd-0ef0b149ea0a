[project]
name = "alertgraph"
version = "0.1.0"
description = "图数据库及其相关接口开发"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.12.9",
    "fastapi[standard]>=0.115.12",
    "neo4j>=5.28.1",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "requests>=2.32.3",
    "uvicorn[standard]>=0.24.0",
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
addopts = [
    "--cov=app",
    "--cov-report=html",
    "--cov-report=term-missing",
    "-v"
]

[[tool.uv.index]]
url = "https://pypi.mirrors.ustc.edu.cn/simple"
default = true

[dependency-groups]
test = [
    "httpx>=0.28.1",
    "pytest>=8.4.0",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.1.1",
    "pytest-mock>=3.14.1",
]
