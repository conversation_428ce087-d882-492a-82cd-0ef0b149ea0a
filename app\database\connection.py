"""
Neo4j数据库连接管理器

负责Neo4j数据库连接管理、连接池维护、事务处理等基础功能
"""

import logging
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager
from neo4j import GraphDatabase, Driver, Session, Transaction, Record
from neo4j.exceptions import Neo4jError, ServiceUnavailable

from app.core.config import settings

logger = logging.getLogger(__name__)


class Neo4jConnection:
    """Neo4j连接管理器"""
    
    def __init__(self, uri: str = None, user: str = None, password: str = None, database: str = None):
        """
        初始化Neo4j连接
        
        Args:
            uri: Neo4j连接URI
            user: 用户名
            password: 密码  
            database: 数据库名
        """
        self.uri = uri or settings.neo4j_uri
        self.user = user or settings.neo4j_user
        self.password = password or settings.neo4j_password
        self.database = database or settings.neo4j_database
        self._driver: Optional[Driver] = None
        
    def connect(self) -> None:
        """建立数据库连接"""
        try:
            self._driver = GraphDatabase.driver(
                self.uri,
                auth=(self.user, self.password)
            )
            # 验证连接
            with self._driver.session(database=self.database) as session:
                session.run("RETURN 1")
            logger.info(f"成功连接到Neo4j数据库: {self.uri}")
        except Exception as e:
            logger.error(f"连接Neo4j数据库失败: {e}")
            raise
    
    def get_driver(self) -> Driver:
        """获取驱动实例"""
        if not self._driver:
            self.connect()
        return self._driver
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self._driver:
            self._driver.close()
            self._driver = None
            logger.info("Neo4j连接已关闭")
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        try:
            if not self._driver:
                return False
            with self._driver.session(database=self.database) as session:
                session.run("RETURN 1")
            return True
        except Exception:
            return False
    
    def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Record]:
        """
        执行查询语句
        
        Args:
            query: Cypher查询语句
            parameters: 查询参数
            
        Returns:
            查询结果列表
        """
        try:
            driver = self.get_driver()
            with driver.session(database=self.database) as session:
                result = session.run(query, parameters or {})
                return list(result)
        except Neo4jError as e:
            logger.error(f"执行查询失败: {query}, 错误: {e}")
            raise
    
    def execute_write_transaction(self, query: str, parameters: Dict[str, Any] = None) -> List[Record]:
        """
        执行写事务
        
        Args:
            query: Cypher写操作语句
            parameters: 查询参数
            
        Returns:
            操作结果
        """
        def _write_tx(tx: Transaction) -> List[Record]:
            result = tx.run(query, parameters or {})
            return list(result)
        
        try:
            driver = self.get_driver()
            with driver.session(database=self.database) as session:
                return session.execute_write(_write_tx)
        except Neo4jError as e:
            logger.error(f"执行写事务失败: {query}, 错误: {e}")
            raise
    
    def execute_read_transaction(self, query: str, parameters: Dict[str, Any] = None) -> List[Record]:
        """
        执行读事务
        
        Args:
            query: Cypher读操作语句
            parameters: 查询参数
            
        Returns:
            查询结果
        """
        def _read_tx(tx: Transaction) -> List[Record]:
            result = tx.run(query, parameters or {})
            return list(result)
        
        try:
            driver = self.get_driver()
            with driver.session(database=self.database) as session:
                return session.execute_read(_read_tx)
        except Neo4jError as e:
            logger.error(f"执行读事务失败: {query}, 错误: {e}")
            raise
    
    def execute_batch_write(self, queries: List[Dict[str, Any]]) -> List[List[Record]]:
        """
        批量执行写操作
        
        Args:
            queries: 查询列表，每个元素包含query和parameters
            
        Returns:
            所有操作的结果列表
        """
        def _batch_write_tx(tx: Transaction) -> List[List[Record]]:
            results = []
            for query_data in queries:
                query = query_data.get('query', '')
                parameters = query_data.get('parameters', {})
                result = tx.run(query, parameters)
                results.append(list(result))
            return results
        
        try:
            driver = self.get_driver()
            with driver.session(database=self.database) as session:
                return session.execute_write(_batch_write_tx)
        except Neo4jError as e:
            logger.error(f"批量写操作失败: {e}")
            raise
    
    @asynccontextmanager
    async def get_session(self):
        """获取会话上下文管理器"""
        driver = self.get_driver()
        session = driver.session(database=self.database)
        try:
            yield session
        finally:
            session.close()
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            driver = self.get_driver()
            with driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                
                # 获取数据库信息
                db_info = session.run("CALL dbms.components()").data()
                
                return {
                    "status": "healthy",
                    "database": self.database,
                    "connection": "active",
                    "test_query": record["test"] if record else None,
                    "components": db_info
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "database": self.database,
                "connection": "failed",
                "error": str(e)
            }


# 全局连接实例
neo4j_connection = Neo4jConnection() 