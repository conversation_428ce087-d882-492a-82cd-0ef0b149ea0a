"""
告警相关数据模型

定义告警细节、研判记录等数据模型
"""

from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field, field_validator

from .base import (
    TimestampedModel, 
    AlertStatus, 
    Severity, 
    Impact, 
    Risk,
    VerdictType,
    VerdictLabel
)


# Evidence Artifacts 相关模型
class HttpHeaderModel(BaseModel):
    """HTTP Header 模型"""
    name: str = Field(..., description="头部名称")
    value: str = Field(..., description="头部值")


class UrlModel(BaseModel):
    """URL 模型"""
    id: Optional[str] = Field(None, description="URL ID")
    url_string: str = Field(..., description="URL字符串")
    categories: Optional[List[str]] = Field(None, description="网站分类")
    domain: Optional[str] = Field(None, description="域名")
    subdomain: Optional[str] = Field(None, description="子域")
    hostname: Optional[str] = Field(None, description="主机名")
    path: Optional[str] = Field(None, description="路径")
    port: Optional[int] = Field(None, description="端口")
    query_string: Optional[str] = Field(None, description="查询字符串")
    scheme: Optional[str] = Field(None, description="协议类型")


class HttpRequestModel(BaseModel):
    """HTTP Request 模型"""
    args: Optional[str] = Field(None, description="HTTP参数")
    body: Optional[str] = Field(None, description="请求体")
    body_length: Optional[int] = Field(None, description="请求体长度")
    http_headers: Optional[List[HttpHeaderModel]] = Field(None, description="HTTP头部")
    http_method: Optional[str] = Field(None, description="HTTP方法")
    length: Optional[int] = Field(None, description="请求长度")
    referer: Optional[str] = Field(None, description="HTTP引用")
    uid: Optional[str] = Field(None, description="uid")
    url: Optional[UrlModel] = Field(None, description="URL")
    user_agent: Optional[str] = Field(None, description="User Agent")
    version: Optional[str] = Field(None, description="版本")
    x_forwarded_for: Optional[List[str]] = Field(None, description="X-Forwarded-For")


class HttpResponseModel(BaseModel):
    """HTTP Response 模型"""
    body: Optional[str] = Field(None, description="响应体")
    body_length: Optional[int] = Field(None, description="响应体长度")
    code: Optional[int] = Field(None, description="响应码")
    content_type: Optional[str] = Field(None, description="内容类型")
    http_headers: Optional[List[HttpHeaderModel]] = Field(None, description="HTTP头部")
    length: Optional[int] = Field(None, description="响应长度")
    message: Optional[str] = Field(None, description="消息")
    status: Optional[str] = Field(None, description="状态")


class DnsQueryModel(BaseModel):
    """DNS Query 模型"""
    class_: Optional[str] = Field(None, alias="class", description="资源记录类")
    hostname: Optional[str] = Field(None, description="主机名")
    opcode: Optional[str] = Field(None, description="DNS操作码")
    opcode_id: Optional[int] = Field(None, description="DNS操作码ID")
    packet_uid: Optional[int] = Field(None, description="数据包唯一标识符")
    type: Optional[str] = Field(None, description="资源记录类型")


class AutonomousSystemModel(BaseModel):
    """自治系统模型"""
    name: Optional[str] = Field(None, description="组织名称")
    number: Optional[str] = Field(None, description="AS标识号")


class GeoLocationModel(BaseModel):
    """地理位置模型"""
    city: Optional[str] = Field(None, description="城市")
    continent: Optional[str] = Field(None, description="大洲")
    country: Optional[str] = Field(None, description="国家")
    desc: Optional[str] = Field(None, description="描述")
    lat: Optional[float] = Field(None, description="纬度")
    long: Optional[float] = Field(None, description="经度")
    postal_code: Optional[str] = Field(None, description="邮政编码")
    provider: Optional[str] = Field(None, description="提供商")
    region: Optional[str] = Field(None, description="地区")


class OperatingSystemModel(BaseModel):
    """操作系统模型"""
    name: Optional[str] = Field(None, description="系统名称")
    type_id: Optional[int] = Field(None, description="系统类型ID")


class NetworkEndpointModel(BaseModel):
    """网络端点模型"""
    autonomous_system: Optional[AutonomousSystemModel] = Field(None, description="自治系统")
    domain: Optional[str] = Field(None, description="域")
    hostname: Optional[str] = Field(None, description="主机名")
    ip: Optional[str] = Field(None, description="IP地址")
    isp: Optional[str] = Field(None, description="ISP")
    isp_org: Optional[str] = Field(None, description="ISP组织")
    location: Optional[GeoLocationModel] = Field(None, description="地理位置")
    mac: Optional[str] = Field(None, description="MAC地址")
    name: Optional[str] = Field(None, description="名称")
    os: Optional[OperatingSystemModel] = Field(None, description="操作系统")
    port: Optional[int] = Field(None, description="端口")
    type: Optional[str] = Field(None, description="类型")
    type_id: Optional[int] = Field(None, description="类型ID")
    uid: Optional[str] = Field(None, description="uid")


class FingerprintModel(BaseModel):
    """指纹模型"""
    algorithm: Optional[str] = Field(None, description="算法")
    algorithm_id: Optional[int] = Field(None, description="算法ID")
    value: Optional[str] = Field(None, description="哈希值")


class FileModel(BaseModel):
    """文件模型"""
    id: Optional[str] = Field(None, description="文件ID")
    name: Optional[str] = Field(None, description="文件名")
    path: Optional[str] = Field(None, description="文件路径")
    hashes: Optional[List[FingerprintModel]] = Field(None, description="文件哈希")
    confidentiality: Optional[str] = Field(None, description="机密性")
    confidentiality_id: Optional[int] = Field(None, description="机密性ID")
    accessed_time: Optional[datetime] = Field(None, description="访问时间")
    created_time: Optional[datetime] = Field(None, description="创建时间")
    modified_time: Optional[datetime] = Field(None, description="修改时间")
    ext: Optional[str] = Field(None, description="扩展名")
    type: Optional[str] = Field(None, description="类型")
    type_id: Optional[int] = Field(None, description="类型ID")
    size: Optional[int] = Field(None, description="文件大小")
    content: Optional[str] = Field(None, description="文件内容")


class EnvironmentVariableModel(BaseModel):
    """环境变量模型"""
    name: str = Field(..., description="变量名")
    value: str = Field(..., description="变量值")


class UserModel(BaseModel):
    """用户模型"""
    id: Optional[str] = Field(None, description="用户ID")
    uid: Optional[str] = Field(None, description="用户唯一ID")
    name: Optional[str] = Field(None, description="用户名")
    type: Optional[str] = Field(None, description="用户类型")
    type_id: Optional[int] = Field(None, description="用户类型ID")
    has_mfa: Optional[bool] = Field(None, description="是否有多因素认证")


class ProcessModel(BaseModel):
    """进程模型"""
    name: Optional[str] = Field(None, description="进程名称")
    path: Optional[str] = Field(None, description="进程路径")
    pid: Optional[int] = Field(None, description="进程ID")
    file: Optional[FileModel] = Field(None, description="进程文件")
    cmd_line: Optional[str] = Field(None, description="命令行")
    created_time: Optional[datetime] = Field(None, description="创建时间")
    environment_variables: Optional[List[EnvironmentVariableModel]] = Field(None, description="环境变量")
    working_directory: Optional[str] = Field(None, description="工作目录")
    user: Optional[UserModel] = Field(None, description="执行用户")
    stack: Optional[str] = Field(None, description="堆栈")
    tree_path: Optional[str] = Field(None, description="进程树")


class EvidenceArtifactsModel(BaseModel):
    """Evidence Artifacts 模型 - 基于test_evidence.md设计"""
    id: str = Field(..., description="Evidence ID")
    type: str = Field(..., description="Evidence 类型")
    is_alert_trigger: Optional[bool] = Field(None, description="是否是告警的直接触发原因")
    include_payload: Optional[bool] = Field(None, description="是否包含攻击载荷")
    
    # HTTP相关
    http_request: Optional[HttpRequestModel] = Field(None, description="HTTP请求")
    http_response: Optional[HttpResponseModel] = Field(None, description="HTTP响应")
    
    # DNS相关
    query: Optional[DnsQueryModel] = Field(None, description="DNS查询")
    
    # 网络端点相关
    src_endpoint: Optional[NetworkEndpointModel] = Field(None, description="源端点")
    dst_endpoint: Optional[NetworkEndpointModel] = Field(None, description="目标端点")
    
    # URL相关
    url: Optional[UrlModel] = Field(None, description="URL")
    
    # 文件相关
    file: Optional[FileModel] = Field(None, description="文件")
    
    # 进程相关
    process: Optional[ProcessModel] = Field(None, description="进程")
    
    # 用户相关
    user: Optional[UserModel] = Field(None, description="用户")


class AlertDetailBase(BaseModel):
    """告警细节基础模型"""
    alert_detail_id: str = Field(..., description="告警细节ID")
    alert_name: str = Field(..., description="告警名称")
    alert_message: str = Field(..., description="告警描述")
    vulnerabilities: Optional[str] = Field(None, description="漏洞描述")
    product_name: str = Field(..., description="设备名称")
    confidence_score: int = Field(..., ge=0, le=100, description="置信度分数 0-100")
    impact: Impact = Field(..., description="影响等级")
    risk: Risk = Field(..., description="风险等级")
    severity: Severity = Field(..., description="严重性")
    status: AlertStatus = Field(AlertStatus.NEW, description="处理状态")
    comment: Optional[str] = Field(None, description="评论")
    raw_data: Optional[str] = Field(None, description="原始推送数据")


class AlertDetailCreate(AlertDetailBase):
    """创建告警细节请求模型"""
    # 新增 evidence_artifacts 字段
    evidence_artifacts: Optional[List[EvidenceArtifactsModel]] = Field(None, description="证据制品列表")


class AlertDetailUpdate(BaseModel):
    """更新告警细节请求模型"""
    alert_name: Optional[str] = Field(None, description="告警名称")
    alert_message: Optional[str] = Field(None, description="告警描述")
    vulnerabilities: Optional[str] = Field(None, description="漏洞描述")
    confidence_score: Optional[int] = Field(None, ge=0, le=100, description="置信度分数")
    impact: Optional[Impact] = Field(None, description="影响等级")
    risk: Optional[Risk] = Field(None, description="风险等级")
    severity: Optional[Severity] = Field(None, description="严重性")
    status: Optional[AlertStatus] = Field(None, description="处理状态")
    comment: Optional[str] = Field(None, description="评论")


class AlertDetailResponse(AlertDetailBase, TimestampedModel):
    """告警细节响应模型"""
    vid: str = Field(..., description="图数据库节点ID")
    time: datetime = Field(..., description="告警时间")
    
    model_config = {"from_attributes": True}


class VerdictBase(BaseModel):
    """研判记录基础模型"""
    type_id: VerdictType = Field(..., description="研判类型: 1-AI, 2-人工")
    type: str = Field(..., description="研判类型说明: AI研判/人工研判")
    label: VerdictLabel = Field(..., description="研判标签: 1-误报, 2-确认攻击, 3-可疑, 4-数据不足")
    reason: str = Field(..., description="研判理由")
    commiter: str = Field(..., description="研判提交者")
    comment: Optional[str] = Field(None, description="研判评论")


class VerdictCreate(VerdictBase):
    """创建研判记录请求模型"""
    pass


class VerdictUpdate(BaseModel):
    """更新研判记录请求模型"""
    type_id: Optional[VerdictType] = Field(None, description="研判类型")
    type: Optional[str] = Field(None, description="研判类型说明")
    label: Optional[VerdictLabel] = Field(None, description="研判标签")
    reason: Optional[str] = Field(None, description="研判理由")
    commiter: Optional[str] = Field(None, description="研判提交者")
    comment: Optional[str] = Field(None, description="研判评论")


class VerdictResponse(VerdictBase, TimestampedModel):
    """研判记录响应模型"""
    verdict_id: str = Field(..., description="研判记录ID")
    time: datetime = Field(..., description="研判时间")
    
    model_config = {"from_attributes": True}


class EvidenceBase(BaseModel):
    """证据信息基础模型"""
    evidence_type: str = Field(..., description="证据类型")
    raw_evidence_data: str = Field(..., description="原始证据数据JSON")


class EvidenceCreate(EvidenceBase):
    """创建证据信息请求模型"""
    pass


class EvidenceResponse(EvidenceBase, TimestampedModel):
    """证据信息响应模型"""
    evidence_id: str = Field(..., description="证据ID")
    created_time: datetime = Field(..., description="创建时间")
    
    model_config = {"from_attributes": True}


class DeviceBase(BaseModel):
    """设备信息基础模型"""
    uuid: Optional[str] = Field(None, description="设备UUID")
    org: Optional[str] = Field(None, description="组织")
    ip: Optional[str] = Field(None, description="主要IP")
    hostname: Optional[str] = Field(None, description="主机名")
    mac: Optional[str] = Field(None, description="MAC地址")
    os_name: Optional[str] = Field(None, description="系统名称")
    count: int = Field(0, description="告警数量统计")


class DeviceCreate(DeviceBase):
    """创建设备信息请求模型"""
    pass


class DeviceResponse(DeviceBase):
    """设备信息响应模型"""
    device_id: str = Field(..., description="设备ID")
    
    model_config = {"from_attributes": True}


class AlertSearchFilters(BaseModel):
    """告警搜索过滤器"""
    alert_name: Optional[str] = Field(None, description="告警名称（模糊匹配）")
    status: Optional[AlertStatus] = Field(None, description="告警状态")
    severity: Optional[Severity] = Field(None, description="严重性")
    risk: Optional[Risk] = Field(None, description="风险等级")
    impact: Optional[Impact] = Field(None, description="影响等级")
    product_name: Optional[str] = Field(None, description="设备名称")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    confidence_score_min: Optional[int] = Field(None, ge=0, le=100, description="最小置信度")
    confidence_score_max: Optional[int] = Field(None, ge=0, le=100, description="最大置信度")
    
    @field_validator('end_time')
    @classmethod
    def validate_time_range(cls, v, info):
        if v and info.data.get('start_time') and v < info.data['start_time']:
            raise ValueError('结束时间不能早于开始时间')
        return v


class BatchCreateResponse(BaseModel):
    """批量创建响应模型"""
    success_count: int = Field(..., description="成功创建数量")
    failed_count: int = Field(0, description="失败数量")
    created_ids: List[str] = Field([], description="成功创建的ID列表")
    errors: List[Dict[str, Any]] = Field([], description="错误信息列表")


class BatchVerdictResponse(BaseModel):
    """批量研判响应模型"""
    success_count: int = Field(..., description="成功应用数量")
    failed_count: int = Field(0, description="失败数量")
    applied_alert_ids: List[str] = Field([], description="成功应用研判的告警ID列表")
    errors: List[Dict[str, Any]] = Field([], description="错误信息列表") 