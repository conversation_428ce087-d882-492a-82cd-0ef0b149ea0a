"""
系统管理API路由

提供数据库状态查看、测试环境管理、系统监控等管理功能
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from app.database.connection import Neo4jConnection
from app.api.dependencies import get_neo4j_connection
from app.utils.logger import logger
# 移除了复杂的测试管理器依赖

router = APIRouter(prefix="/system", tags=["系统管理"])


@router.get("/health", response_model=Dict[str, Any])
def get_system_health(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """获取系统健康状态"""
    try:
        health_result = connection.health_check()
        return {
            "system": "AlertGraph",
            "status": "healthy",
            "timestamp": health_result.get("timestamp"),
            "database": health_result,
            "version": "0.1.0"
        }
    except Exception as e:
        logger.error("系统健康检查失败", error=e)
        raise HTTPException(status_code=500, detail=f"系统健康检查失败: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
def get_system_stats(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """获取系统统计信息"""
    try:
        # 获取各种节点数量统计
        alert_query = "MATCH (a:AlertDetail) RETURN count(a) as count"
        device_query = "MATCH (d:Device) RETURN count(d) as count"
        entity_query = "MATCH (e:Entity) RETURN count(e) as count"
        evidence_query = "MATCH (ev:Evidence) RETURN count(ev) as count"
        
        alert_result = connection.execute_read_transaction(alert_query)
        device_result = connection.execute_read_transaction(device_query)
        entity_result = connection.execute_read_transaction(entity_query)
        evidence_result = connection.execute_read_transaction(evidence_query)
        
        alert_count = alert_result[0]["count"] if alert_result else 0
        device_count = device_result[0]["count"] if device_result else 0
        entity_count = entity_result[0]["count"] if entity_result else 0
        evidence_count = evidence_result[0]["count"] if evidence_result else 0
        
        from datetime import datetime
        return {
            "alert_count": alert_count,
            "device_count": device_count,
            "entity_count": entity_count,
            "evidence_count": evidence_count,
            "total_nodes": alert_count + device_count + entity_count + evidence_count,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error("获取系统统计失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取系统统计失败: {str(e)}")


@router.get("/database/status", response_model=Dict[str, Any])
def get_database_status(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """获取数据库状态信息"""
    try:
        health_result = connection.health_check()
        
        return {
            "database": "Neo4j",
            "connection": "active" if health_result.get("status") == "healthy" else "inactive",
            "statistics": health_result,
            "timestamp": health_result.get("timestamp")
        }
    except Exception as e:
        logger.error("获取数据库状态失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取数据库状态失败: {str(e)}")


@router.get("/database/nodes", response_model=Dict[str, Any])
def get_node_statistics(
    label: Optional[str] = Query(None, description="节点标签"),
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """获取节点统计信息"""
    try:
        if label:
            query = f"MATCH (n:{label}) RETURN count(n) as count"
            result = connection.execute_read_transaction(query)
            count = result[0]["count"] if result else 0
            return {
                "label": label,
                "count": count
            }
        else:
            query = "MATCH (n) RETURN labels(n) as labels, count(n) as count"
            result = connection.execute_read_transaction(query)
            node_stats = {}
            total = 0
            for record in result:
                labels = record["labels"]
                count = record["count"]
                for label in labels:
                    node_stats[label] = node_stats.get(label, 0) + count
                total += count
            
            return {
                "node_statistics": node_stats,
                "total_nodes": total
            }
    except Exception as e:
        logger.error("获取节点统计失败", error=e, label=label)
        raise HTTPException(status_code=500, detail=f"获取节点统计失败: {str(e)}")


@router.get("/database/relationships", response_model=Dict[str, Any])
def get_relationship_statistics(
    rel_type: Optional[str] = Query(None, description="关系类型"),
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """获取关系统计信息"""
    try:
        if rel_type:
            query = f"MATCH ()-[r:{rel_type}]-() RETURN count(r) as count"
            result = connection.execute_read_transaction(query)
            count = result[0]["count"] if result else 0
            return {
                "relationship_type": rel_type,
                "count": count
            }
        else:
            query = "MATCH ()-[r]-() RETURN type(r) as rel_type, count(r) as count"
            result = connection.execute_read_transaction(query)
            rel_stats = {}
            total = 0
            for record in result:
                rel_type = record["rel_type"]
                count = record["count"]
                rel_stats[rel_type] = count
                total += count
            
            return {
                "relationship_statistics": rel_stats,
                "total_relationships": total
            }
    except Exception as e:
        logger.error("获取关系统计失败", error=e, rel_type=rel_type)
        raise HTTPException(status_code=500, detail=f"获取关系统计失败: {str(e)}")


# 测试环境管理API（仅在测试模式下可用）
@router.post("/test/reset-database", response_model=Dict[str, Any])
def reset_test_database(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """重置测试数据库（仅测试环境）"""
    import os
    if not os.getenv("TESTING"):
        raise HTTPException(status_code=403, detail="此功能仅在测试环境下可用")
    
    try:
        # 简化的数据库重置
        query = "MATCH (n) DETACH DELETE n"
        connection.execute_write_transaction(query)
        
        return {
            "success": True,
            "message": "测试数据库重置完成",
            "timestamp": "2024-01-01T00:00:00"
        }
    except Exception as e:
        logger.error("重置测试数据库失败", error=e)
        raise HTTPException(status_code=500, detail=f"重置测试数据库失败: {str(e)}")


@router.delete("/test/clean", response_model=Dict[str, Any])
def clean_test_data(
    labels: Optional[List[str]] = Query(None, description="要清理的节点标签"),
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """清理测试数据（仅测试环境）"""
    import os
    if not os.getenv("TESTING"):
        raise HTTPException(status_code=403, detail="此功能仅在测试环境下可用")
    
    try:
        if labels:
            # 清理指定标签的节点
            for label in labels:
                query = f"MATCH (n:{label}) DETACH DELETE n"
                connection.execute_write_transaction(query)
            message = f"清理指定标签数据完成: {labels}"
        else:
            # 清理所有数据
            query = "MATCH (n) DETACH DELETE n"
            connection.execute_write_transaction(query)
            message = "清理所有测试数据完成"
        
        return {
            "success": True,
            "message": message,
            "cleaned_labels": labels
        }
    except Exception as e:
        logger.error("清理测试数据失败", error=e, labels=labels)
        raise HTTPException(status_code=500, detail=f"清理测试数据失败: {str(e)}")


@router.get("/test/status", response_model=Dict[str, Any])
def get_test_environment_status(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
):
    """获取测试环境状态（仅测试环境）"""
    import os
    if not os.getenv("TESTING"):
        raise HTTPException(status_code=403, detail="此功能仅在测试环境下可用")
    
    try:
        health_result = connection.health_check()
        
        return {
            "test_environment": True,
            "database_status": health_result,
            "testing_enabled": bool(os.getenv("TESTING")),
            "log_level": os.getenv("LOG_LEVEL", "INFO")
        }
    except Exception as e:
        logger.error("获取测试环境状态失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取测试环境状态失败: {str(e)}")


@router.get("/logs/recent", response_model=Dict[str, Any])
def get_recent_logs(
    limit: int = Query(100, description="日志条数限制", ge=1, le=1000),
    level: Optional[str] = Query(None, description="日志级别"),
):
    """获取最近的日志记录"""
    try:
        # 这里应该从日志存储中读取，暂时返回示例数据
        logs = [
            {
                "timestamp": "2024-01-01T12:00:00",
                "level": "INFO",
                "message": "API请求处理完成",
                "service": "alertgraph",
                "module": "api"
            }
        ]
        
        return {
            "logs": logs[:limit],
            "total_count": len(logs),
            "limit": limit,
            "level_filter": level
        }
    except Exception as e:
        logger.error("获取日志记录失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取日志记录失败: {str(e)}")


@router.get("/performance/metrics", response_model=Dict[str, Any])
def get_performance_metrics():
    """获取性能指标"""
    try:
        # 这里应该从监控系统获取实际指标
        metrics = {
            "api_response_time": {
                "average": 150,
                "p95": 300,
                "p99": 500
            },
            "database_query_time": {
                "average": 50,
                "p95": 100,
                "p99": 200
            },
            "memory_usage": {
                "used": 512,
                "total": 2048,
                "percentage": 25
            },
            "request_count": {
                "total": 1000,
                "successful": 950,
                "failed": 50
            }
        }
        
        return {
            "metrics": metrics,
            "timestamp": "2024-01-01T12:00:00",
            "collection_interval": "1m"
        }
    except Exception as e:
        logger.error("获取性能指标失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}") 