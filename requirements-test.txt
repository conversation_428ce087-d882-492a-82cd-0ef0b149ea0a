# AlertGraph 测试依赖

# 测试框架
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
pytest-xdist>=3.0.0  # 并行测试
pytest-mock>=3.10.0

# HTTP客户端测试
httpx>=0.24.0

# 覆盖率报告
coverage>=7.0.0

# 测试工具
factory-boy>=3.2.0  # 测试数据工厂
faker>=18.0.0       # 假数据生成

# 开发工具
black>=23.0.0       # 代码格式化
isort>=5.12.0       # 导入排序
flake8>=6.0.0       # 代码检查
mypy>=1.0.0         # 类型检查

# 文档生成
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
