"""
实体管理API路由

提供实体相关的REST API接口
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from app.models.base import ResponseModel
from app.api.dependencies import get_entity_service
from app.services.entity_service import EntityService
from app.models.entity import (
    EntityCreate,
    EntityUpdate, 
    EntityResponse,
    RelationshipCreate,
    RelationshipResponse,
    EntitySearchParams,
    PathResponse
)
from app.models.alert import BatchCreateResponse
from app.models.base import EntityType

router = APIRouter(prefix="/entities", tags=["实体管理"])


@router.post("/", response_model=EntityResponse)
def create_entity(
    entity_data: EntityCreate,
    entity_service: EntityService = Depends(get_entity_service)
):
    """创建实体"""
    try:
        result = entity_service.create_entity(entity_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建实体失败: {str(e)}")


@router.post("/batch", response_model=BatchCreateResponse)  
def batch_create_entities(
    entities: List[EntityCreate],
    entity_service: EntityService = Depends(get_entity_service)
):
    """批量创建实体"""
    try:
        result = entity_service.batch_create_entities(entities)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量创建实体失败: {str(e)}")


@router.get("/{entity_id}", response_model=EntityResponse)
def get_entity(
    entity_id: str,
    entity_service: EntityService = Depends(get_entity_service)
):
    """获取实体详情"""
    try:
        entity = entity_service.get_entity_by_id(entity_id)
        if not entity:
            raise HTTPException(status_code=404, detail="实体不存在")
        return entity
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实体失败: {str(e)}")


@router.put("/{entity_id}", response_model=EntityResponse)
def update_entity(
    entity_id: str,
    update_data: EntityUpdate,
    entity_service: EntityService = Depends(get_entity_service)
):
    """更新实体"""
    try:
        entity = entity_service.update_entity(entity_id, update_data)
        if not entity:
            raise HTTPException(status_code=404, detail="实体不存在")
        return entity
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新实体失败: {str(e)}")


@router.get("/", response_model=List[EntityResponse])
def search_entities(
    entity_type: Optional[EntityType] = Query(None, description="实体类型"),
    properties: Optional[str] = Query(None, description="属性过滤条件（JSON格式）"),
    limit: int = Query(50, ge=1, le=1000, description="返回数量限制"),
    entity_service: EntityService = Depends(get_entity_service)
):
    """搜索实体"""
    try:
        import json
        search_params = EntitySearchParams(
            entity_type=entity_type,
            properties=json.loads(properties) if properties else None,
            limit=limit
        )
        result = entity_service.search_entities(search_params)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索实体失败: {str(e)}")


@router.get("/types/{entity_type}", response_model=List[EntityResponse])
def get_entities_by_type(
    entity_type: EntityType,
    entity_service: EntityService = Depends(get_entity_service)
):
    """按类型获取实体列表"""
    try:
        result = entity_service.get_entities_by_type(entity_type.value)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"按类型查询实体失败: {str(e)}")


@router.post("/relationships", response_model=RelationshipResponse)
def create_relationship(
    rel_data: RelationshipCreate,
    entity_service: EntityService = Depends(get_entity_service)
):
    """创建实体关系"""
    try:
        result = entity_service.create_relationship(rel_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建实体关系失败: {str(e)}")


@router.get("/{entity_id}/relationships", response_model=List[RelationshipResponse])
def get_entity_relationships(
    entity_id: str,
    entity_service: EntityService = Depends(get_entity_service)
):
    """获取实体关系"""
    try:
        result = entity_service.get_entity_relationships(entity_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实体关系失败: {str(e)}")


@router.get("/path/{from_entity_id}/{to_entity_id}", response_model=List[PathResponse])
def find_entity_path(
    from_entity_id: str,
    to_entity_id: str,
    max_depth: int = Query(5, ge=1, le=10, description="最大深度"),
    entity_service: EntityService = Depends(get_entity_service)
):
    """查找实体间路径"""
    try:
        result = entity_service.find_entity_path(from_entity_id, to_entity_id, max_depth)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查找实体路径失败: {str(e)}") 