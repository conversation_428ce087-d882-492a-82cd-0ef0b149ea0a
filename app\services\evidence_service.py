"""
证据管理服务

实现证据信息的处理、风险实体提取和关联等业务逻辑
"""

import json
import hashlib
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from uuid import uuid4, uuid5, NAMESPACE_DNS

from app.database.connection import Neo4jConnection
from app.repositories.base import BaseRepository
from app.models.alert import EvidenceArtifactsModel, AlertDetailCreate
from app.models.entity import EntityCreate, EntityResponse
from app.models.base import EntityType
from app.core.constants import NodeLabels, RelationshipTypes
from app.utils.logger import logger


class EvidenceService:
    """证据管理服务"""

    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
        self.repository = BaseRepository(connection)
        # 为了避免循环导入，这里延迟初始化EntityService
        self.entity_service = None

    def process_evidence_artifacts(
        self, alert_id: str, evidence_artifacts: List[EvidenceArtifactsModel]
    ) -> str:
        """
        处理证据制品列表，创建单个Evidence节点
        返回创建的evidence节点ID
        """
        # 简化日志 - 只记录关键信息
        evidence_id = self._create_evidence_node(alert_id, evidence_artifacts)

        # 从所有evidence_artifacts中提取风险实体
        all_entities = []
        for evidence_artifact in evidence_artifacts:
            entities = self._extract_risk_entities(evidence_artifact)
            all_entities.extend(entities)

        # 只在有实体时记录
        if all_entities:
            logger.debug(
                f"从证据中提取到 {len(all_entities)} 个风险实体",
                alert_id=alert_id,
                evidence_id=evidence_id,
            )

        # 创建或关联风险实体（只和证据节点关联）
        for entity_data in all_entities:
            entity_id, is_new = self._create_or_get_risk_entity(entity_data)
            self._link_evidence_to_entity(
                evidence_id, entity_id, entity_data["entity_type"], entity_data, is_new
            )
        return evidence_id

    def _create_evidence_node(
        self, alert_id: str, evidence_artifacts: List[EvidenceArtifactsModel]
    ) -> str:
        """创建单个evidence节点，包含所有evidence_artifacts内容"""
        current_time = datetime.now()

        # 生成唯一的evidence ID（基于所有evidence内容）
        all_evidence_content = self._get_all_evidence_content_for_id(evidence_artifacts)
        evidence_id = self._generate_evidence_id(
            "combined_evidence", all_evidence_content
        )

        # 合并所有evidence_artifacts的信息
        evidence_types = []
        has_trigger = False
        has_payload = False
        all_raw_data = []

        for evidence in evidence_artifacts:
            evidence_types.append(evidence.type)
            if evidence.is_alert_trigger:
                has_trigger = True
            if evidence.include_payload:
                has_payload = True
            # 使用mode='json'确保datetime对象被正确序列化为字符串
            all_raw_data.append(evidence.model_dump(mode="json"))

        properties = {
            "evidence_id": evidence_id,
            "types": ",".join(set(evidence_types)),  # 去重的类型列表
            "is_alert_trigger": has_trigger,
            "include_payload": has_payload,
            "raw_data": json.dumps(all_raw_data, ensure_ascii=False),
            "evidence_count": len(evidence_artifacts),
            "created_time": current_time.isoformat(),
            "updated_at": current_time.isoformat(),
        }

        # 添加特定类型的属性（从所有evidence中提取）
        self._add_combined_type_specific_properties(evidence_artifacts, properties)

        # 创建节点
        node_id = self.repository.create_node(NodeLabels.EVIDENCE, properties)

        # 关联到告警细节
        logger.info(
            "创建告警-证据关系",
            alert_id=alert_id,
            evidence_id=evidence_id,
            relationship_type=RelationshipTypes.HAS_EVIDENCE,
        )
        relationship_created = self.repository.create_relationship(
            alert_id,
            evidence_id,
            RelationshipTypes.HAS_EVIDENCE,
            {
                "created_time": current_time.isoformat(),
                "is_trigger": has_trigger,
                "has_payload": has_payload,
                "evidence_count": len(evidence_artifacts),
            },
            "AlertDetail",  # from_label
            "Evidence",  # to_label
        )

        if relationship_created:
            logger.info(
                "告警-证据关系创建成功", alert_id=alert_id, evidence_id=evidence_id
            )
        else:
            logger.error(
                "告警-证据关系创建失败", alert_id=alert_id, evidence_id=evidence_id
            )

        return evidence_id

    def _extract_risk_entities(
        self, evidence: EvidenceArtifactsModel
    ) -> List[Dict[str, Any]]:
        """从evidence中提取风险实体"""
        entities = []

        # 提取HTTP请求实体
        if evidence.http_request:
            entity = self._extract_http_request_entity(evidence.http_request)
            if entity:
                entities.append(entity)

            # 从HTTP请求中提取URL实体
            if evidence.http_request.url:
                url_entity = self._extract_url_entity(evidence.http_request.url)
                if url_entity:
                    entities.append(url_entity)

        # 提取HTTP响应实体
        if evidence.http_response:
            entity = self._extract_http_response_entity(evidence.http_response)
            if entity:
                entities.append(entity)

        # 提取DNS查询实体
        if evidence.query:
            entity = self._extract_dns_query_entity(evidence.query)
            if entity:
                entities.append(entity)

        # 提取网络端点实体
        if evidence.src_endpoint:
            entity = self._extract_network_endpoint_entity(evidence.src_endpoint, "src")
            if entity:
                entities.append(entity)

        if evidence.dst_endpoint:
            entity = self._extract_network_endpoint_entity(evidence.dst_endpoint, "dst")
            if entity:
                entities.append(entity)

        # 提取URL实体
        if evidence.url:
            entity = self._extract_url_entity(evidence.url)
            if entity:
                entities.append(entity)

        # 提取文件实体
        if evidence.file:
            entity = self._extract_file_entity(evidence.file)
            if entity:
                entities.append(entity)

        # 提取进程实体
        if evidence.process:
            entity = self._extract_process_entity(evidence.process)
            if entity:
                entities.append(entity)

            # 从进程中提取文件实体
            if evidence.process.file:
                file_entity = self._extract_file_entity(evidence.process.file)
                if file_entity:
                    entities.append(file_entity)

        # 提取用户实体
        if evidence.user:
            entity = self._extract_user_entity(evidence.user)
            if entity:
                entities.append(entity)

        return entities

    def _extract_http_request_entity(self, http_request) -> Optional[Dict[str, Any]]:
        """提取HTTP请求实体"""
        if not http_request:
            return None

        # 生成实体ID
        content = f"{http_request.http_method or ''}{http_request.url.url_string if http_request.url else ''}{http_request.body or ''}"
        entity_id = self._generate_entity_id("http_request", content)

        properties = {
            "method": http_request.http_method,
            "body": http_request.body,
            "body_length": http_request.body_length,
            "length": http_request.length,
            "user_agent": http_request.user_agent,
            "referer": http_request.referer,
            "version": http_request.version,
        }

        # 添加URL信息
        if http_request.url:
            properties["url"] = http_request.url.url_string
            properties["hostname"] = http_request.url.hostname
            properties["path"] = http_request.url.path

        # 添加头部信息
        if http_request.http_headers:
            headers = {h.name: h.value for h in http_request.http_headers}
            properties["headers"] = json.dumps(headers, ensure_ascii=False)

        # 添加X-Forwarded-For信息
        if http_request.x_forwarded_for:
            properties["x_forwarded_for"] = ",".join(http_request.x_forwarded_for)

        return {
            "entity_id": entity_id,
            "entity_type": EntityType.HTTP_REQUEST,
            "name": f"HTTP {http_request.http_method or 'REQUEST'}",
            "properties": properties,
            "risk_score": self._calculate_http_request_risk_score(http_request),
            "description": f"HTTP请求: {http_request.http_method or 'REQUEST'} {http_request.url.url_string if http_request.url else 'N/A'}",
        }

    def _extract_network_endpoint_entity(
        self, endpoint, prefix: str
    ) -> Optional[Dict[str, Any]]:
        """提取网络端点实体"""
        if not endpoint or not endpoint.ip:
            return None

        # 生成实体ID
        content = f"{endpoint.ip}{endpoint.mac or ''}{endpoint.uid or ''}"
        entity_id = self._generate_entity_id("network_endpoint", content)

        properties = {
            "ip_address": endpoint.ip,  # 匹配EntityService中的匹配规则
            "hostname": endpoint.hostname,
            "mac": endpoint.mac,
            "port": endpoint.port,
            "domain": endpoint.domain,
            "isp": endpoint.isp,
            "isp_org": endpoint.isp_org,
            "type": endpoint.type,
            "type_id": endpoint.type_id,
            "uid": endpoint.uid,
            "endpoint_type": prefix,
        }

        # 添加地理位置信息
        if endpoint.location:
            properties.update(
                {
                    "city": endpoint.location.city,
                    "country": endpoint.location.country,
                    "continent": endpoint.location.continent,
                    "latitude": endpoint.location.lat,
                    "longitude": endpoint.location.long,
                    "region": endpoint.location.region,
                }
            )

        # 添加自治系统信息
        if endpoint.autonomous_system:
            properties.update(
                {
                    "as_name": endpoint.autonomous_system.name,
                    "as_number": endpoint.autonomous_system.number,
                }
            )

        # 添加操作系统信息
        if endpoint.os:
            properties.update(
                {"os_name": endpoint.os.name, "os_type_id": endpoint.os.type_id}
            )

        return {
            "entity_id": entity_id,
            "entity_type": EntityType.NETWORK_ENDPOINT,
            "name": f"{prefix.upper()} {endpoint.ip}",
            "properties": properties,
            "risk_score": self._calculate_network_endpoint_risk_score(endpoint),
            "description": f"网络端点: {endpoint.ip} ({endpoint.hostname or 'N/A'})",
        }

    def _extract_url_entity(self, url) -> Optional[Dict[str, Any]]:
        """提取URL实体"""
        if not url or not url.url_string:
            return None

        # 生成实体ID
        entity_id = self._generate_entity_id("url", url.url_string)

        properties = {
            "full_url": url.url_string,  # 匹配EntityService中的匹配规则
            "url": url.url_string,  # 保留原有字段
            "domain": url.domain,
            "subdomain": url.subdomain,
            "hostname": url.hostname,
            "path": url.path,
            "port": url.port,
            "query_string": url.query_string,
            "scheme": url.scheme,
        }

        # 添加分类信息
        if url.categories:
            properties["categories"] = ",".join(url.categories)

        return {
            "entity_id": entity_id,
            "entity_type": EntityType.URL,
            "name": url.hostname or url.url_string,
            "properties": properties,
            "risk_score": self._calculate_url_risk_score(url),
            "description": f"URL: {url.url_string}",
        }

    def _extract_file_entity(self, file) -> Optional[Dict[str, Any]]:
        """提取文件实体"""
        if not file or (not file.path and not file.name):
            return None

        # 生成实体ID（基于哈希值或路径）
        if file.hashes and len(file.hashes) > 0:
            content = file.hashes[0].value
        else:
            content = file.path or file.name
        entity_id = self._generate_entity_id("file", content)

        properties = {
            "filename": file.name,
            "file_path": file.path,
            "file_size": file.size,
            "file_extension": file.ext,
            "file_type": file.type,
            "mime_type": file.type,
            "confidentiality": file.confidentiality,
            "confidentiality_id": file.confidentiality_id,
        }

        # 添加时间信息
        if file.created_time:
            properties["created_time"] = file.created_time.isoformat()
        if file.modified_time:
            properties["modified_time"] = file.modified_time.isoformat()
        if file.accessed_time:
            properties["accessed_time"] = file.accessed_time.isoformat()

        # 添加哈希信息
        if file.hashes:
            for hash_info in file.hashes:
                properties[f"hash_{hash_info.algorithm.lower()}"] = hash_info.value
                # 使用file_hash作为主要匹配字段
                if hash_info.algorithm.lower() in ["md5", "sha1", "sha256"]:
                    properties["file_hash"] = hash_info.value

        return {
            "entity_id": entity_id,
            "entity_type": EntityType.FILE,
            "name": file.name or "未知文件",
            "properties": properties,
            "risk_score": self._calculate_file_risk_score(file),
            "description": f"文件: {file.name or file.path or '未知'}",
        }

    def _extract_process_entity(self, process) -> Optional[Dict[str, Any]]:
        """提取进程实体"""
        if not process or not process.name:
            return None

        # 生成实体ID
        content = f"{process.name}{process.path or ''}{process.cmd_line or ''}"
        entity_id = self._generate_entity_id("process", content)

        properties = {
            "process_name": process.name,
            "process_path": process.path,
            "process_id": process.pid,
            "command_line": process.cmd_line,
            "working_directory": process.working_directory,
            "stack": process.stack,
            "tree_path": process.tree_path,
        }

        # 添加用户信息
        if process.user:
            properties.update(
                {
                    "user_name": process.user.name,
                    "user_type": process.user.type,
                    "user_id": process.user.uid,
                }
            )

        # 添加创建时间
        if process.created_time:
            properties["process_created_time"] = process.created_time.isoformat()

        # 添加环境变量
        if process.environment_variables:
            env_vars = {var.name: var.value for var in process.environment_variables}
            properties["environment_variables"] = json.dumps(
                env_vars, ensure_ascii=False
            )

        return {
            "entity_id": entity_id,
            "entity_type": EntityType.PROCESS,
            "name": process.name,
            "properties": properties,
            "risk_score": self._calculate_process_risk_score(process),
            "description": f"进程: {process.name} (PID: {process.pid or 'N/A'})",
        }

    def _create_or_get_risk_entity(
        self, entity_data: Dict[str, Any]
    ) -> tuple[str, bool]:
        """创建或获取风险实体，返回(entity_id, 是否新创建)"""
        # 延迟初始化EntityService避免循环导入
        if self.entity_service is None:
            from app.services.entity_service import EntityService

            self.entity_service = EntityService(self.connection)

        # 创建EntityCreate对象
        entity_create = EntityCreate(
            entity_type=entity_data["entity_type"],
            name=entity_data["name"],
            properties=entity_data["properties"],
            risk_score=entity_data["risk_score"],
            description=entity_data["description"],
        )

        # 使用EntityService的去重机制
        entity_response, is_new = self.entity_service.find_or_create_entity(
            entity_create
        )

        return entity_response.entity_id, is_new

    def _link_evidence_to_entity(
        self,
        evidence_id: str,
        entity_id: str,
        entity_type: EntityType,
        entity_data: dict,
        is_new: bool,
    ):
        """关联evidence到风险实体，记录特征信息"""
        current_time = datetime.now()

        # 准备关系属性，包含特征信息
        rel_properties = {
            "entity_type": entity_type.value,
            "created_time": current_time.isoformat(),
            "extraction_reason": "从证据中提取",
            "is_new_entity": is_new,
            "entity_context": json.dumps(
                {
                    "entity_name": entity_data.get("name", ""),
                    "risk_score": entity_data.get("risk_score", 0),
                    "properties_snapshot": entity_data.get("properties", {}),
                    "extraction_time": current_time.isoformat(),
                },
                ensure_ascii=False,
            ),
        }

        # 创建关系，只在失败时记录日志
        relationship_created = self.repository.create_relationship(
            evidence_id,
            entity_id,
            RelationshipTypes.RELATES_TO,
            rel_properties,
            "Evidence",  # from_label
            self._get_entity_label_from_type(entity_type),  # to_label
        )

        if not relationship_created:
            logger.error(
                "证据-实体关系创建失败",
                evidence_id=evidence_id,
                entity_id=entity_id,
                entity_type=entity_type.value,
            )

    def _generate_evidence_id(self, evidence_type: str, content: str) -> str:
        """生成evidence ID"""
        # 使用UUID5基于类型和内容生成确定性ID
        namespace = uuid5(NAMESPACE_DNS, "evidence")
        unique_string = f"{evidence_type}--{content}"
        return str(uuid5(namespace, unique_string))

    def _generate_entity_id(self, entity_type: str, content: str) -> str:
        """生成entity ID"""
        # 使用UUID5基于类型和内容生成确定性ID
        namespace = uuid5(NAMESPACE_DNS, "entity")
        unique_string = f"{entity_type}--{content}"
        return str(uuid5(namespace, unique_string))

    def _get_all_evidence_content_for_id(
        self, evidence_artifacts: List[EvidenceArtifactsModel]
    ) -> str:
        """获取所有evidence用于生成ID的内容"""
        all_content_parts = []

        for evidence in evidence_artifacts:
            content_parts = []

            if evidence.http_request:
                content_parts.append(evidence.http_request.body or "")
                if evidence.http_request.http_headers:
                    headers_str = "&".join(
                        [
                            f"{h.name}={h.value}"
                            for h in evidence.http_request.http_headers
                        ]
                    )
                    content_parts.append(headers_str)
                content_parts.append(evidence.http_request.http_method or "")
                if evidence.http_request.url:
                    content_parts.append(evidence.http_request.url.url_string)
                content_parts.append(evidence.http_request.version or "")

            if evidence.http_response:
                content_parts.append(evidence.http_response.body or "")
                content_parts.append(str(evidence.http_response.code or ""))

            if evidence.src_endpoint and evidence.src_endpoint.ip:
                content_parts.append(evidence.src_endpoint.ip)

            if evidence.dst_endpoint and evidence.dst_endpoint.ip:
                content_parts.append(evidence.dst_endpoint.ip)

            if evidence.query and evidence.query.hostname:
                content_parts.append(evidence.query.hostname)

            if evidence.file and evidence.file.name:
                content_parts.append(evidence.file.name)

            if evidence.process and evidence.process.name:
                content_parts.append(evidence.process.name)

            all_content_parts.append("".join(content_parts))

        return "|".join(all_content_parts)

    def _add_combined_type_specific_properties(
        self,
        evidence_artifacts: List[EvidenceArtifactsModel],
        properties: Dict[str, Any],
    ):
        """添加特定类型的属性到evidence节点（从所有evidence中提取）"""
        urls = []
        http_methods = []
        src_ips = []
        dst_ips = []
        hostnames = []
        filenames = []
        process_names = []

        for evidence in evidence_artifacts:
            if evidence.http_request:
                if evidence.http_request.url:
                    urls.append(evidence.http_request.url.url_string)
                if evidence.http_request.http_method:
                    http_methods.append(evidence.http_request.http_method)

            if evidence.src_endpoint and evidence.src_endpoint.ip:
                src_ips.append(evidence.src_endpoint.ip)

            if evidence.dst_endpoint and evidence.dst_endpoint.ip:
                dst_ips.append(evidence.dst_endpoint.ip)

            if evidence.query and evidence.query.hostname:
                hostnames.append(evidence.query.hostname)

            if evidence.file and evidence.file.name:
                filenames.append(evidence.file.name)

            if evidence.process and evidence.process.name:
                process_names.append(evidence.process.name)

        # 将列表转换为逗号分隔的字符串（去重）
        if urls:
            properties["urls"] = ",".join(set(urls))
        if http_methods:
            properties["http_methods"] = ",".join(set(http_methods))
        if src_ips:
            properties["src_ips"] = ",".join(set(src_ips))
        if dst_ips:
            properties["dst_ips"] = ",".join(set(dst_ips))
        if hostnames:
            properties["hostnames"] = ",".join(set(hostnames))
        if filenames:
            properties["filenames"] = ",".join(set(filenames))
        if process_names:
            properties["process_names"] = ",".join(set(process_names))

    def _calculate_http_request_risk_score(self, http_request) -> int:
        """计算HTTP请求风险分数"""
        score = 30  # 基础分数

        # 根据请求方法调整
        if http_request.http_method in ["POST", "PUT", "DELETE"]:
            score += 20

        # 根据URL路径调整
        if http_request.url and http_request.url.path:
            suspicious_paths = ["/admin", "/login", "/upload", "/shell", "/cmd"]
            if any(path in http_request.url.path.lower() for path in suspicious_paths):
                score += 30

        return min(score, 100)

    def _calculate_network_endpoint_risk_score(self, endpoint) -> int:
        """计算网络端点风险分数"""
        score = 20  # 基础分数

        # 根据IP类型调整
        if endpoint.ip:
            # 私有IP降低风险
            if endpoint.ip.startswith(("192.168.", "10.", "172.")):
                score -= 10
            # 公网IP增加风险
            else:
                score += 20

        # 根据地理位置调整
        if endpoint.location:
            high_risk_countries = ["CN", "RU", "KP", "IR"]  # 示例
            if endpoint.location.country in high_risk_countries:
                score += 30

        return min(max(score, 0), 100)

    def _calculate_url_risk_score(self, url) -> int:
        """计算URL风险分数"""
        score = 25  # 基础分数

        # 根据协议调整
        if url.scheme == "http":
            score += 15

        # 根据域名调整
        if url.domain:
            suspicious_tlds = [".tk", ".ml", ".ga", ".cf"]
            if any(url.domain.endswith(tld) for tld in suspicious_tlds):
                score += 30

        # 根据路径调整
        if url.path:
            suspicious_paths = ["shell", "exploit", "payload", "hack"]
            if any(path in url.path.lower() for path in suspicious_paths):
                score += 40

        return min(score, 100)

    def _calculate_file_risk_score(self, file) -> int:
        """计算文件风险分数"""
        score = 30  # 基础分数

        # 根据文件扩展名调整
        if file.ext:
            dangerous_exts = [".exe", ".bat", ".cmd", ".scr", ".pif", ".com"]
            if file.ext.lower() in dangerous_exts:
                score += 40

            script_exts = [".js", ".vbs", ".ps1", ".sh"]
            if file.ext.lower() in script_exts:
                score += 20

        # 根据文件大小调整
        if file.size:
            # 非常小的可执行文件可能是恶意软件
            if file.size < 1024 and file.ext and file.ext.lower() in [".exe", ".dll"]:
                score += 30

        return min(score, 100)

    def _calculate_process_risk_score(self, process) -> int:
        """计算进程风险分数"""
        score = 25  # 基础分数

        # 根据进程名调整
        if process.name:
            suspicious_names = [
                "cmd.exe",
                "powershell.exe",
                "wscript.exe",
                "cscript.exe",
            ]
            if process.name.lower() in suspicious_names:
                score += 30

        # 根据命令行调整
        if process.cmd_line:
            suspicious_commands = ["download", "execute", "shell", "exploit"]
            if any(cmd in process.cmd_line.lower() for cmd in suspicious_commands):
                score += 35

        return min(score, 100)

    def _get_entity_by_id(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取实体"""
        query = """
        MATCH (e)
        WHERE e.entity_id = $entity_id
        RETURN e
        """

        result = self.connection.execute_read_transaction(
            query, {"entity_id": entity_id}
        )

        if result:
            return dict(result[0]["e"])
        return None

    def _get_node_label_for_entity_type(self, entity_type: EntityType) -> str:
        """根据实体类型获取节点标签"""
        mapping = {
            EntityType.HTTP_REQUEST: NodeLabels.HTTP_REQUEST_ENTITY,
            EntityType.HTTP_RESPONSE: NodeLabels.HTTP_RESPONSE_ENTITY,
            EntityType.DNS_QUERY: NodeLabels.DNS_QUERY_ENTITY,
            EntityType.NETWORK_ENDPOINT: NodeLabels.NETWORK_ENDPOINT_ENTITY,
            EntityType.URL: NodeLabels.URL_ENTITY,
            EntityType.FILE: NodeLabels.FILE_ENTITY,
            EntityType.PROCESS: NodeLabels.PROCESS_ENTITY,
        }
        return mapping.get(entity_type, NodeLabels.NETWORK_ENDPOINT_ENTITY)

    def _get_entity_label_from_type(self, entity_type: EntityType) -> str:
        """根据实体类型获取实体标签（简化版本，直接返回标签名）"""
        mapping = {
            EntityType.HTTP_REQUEST: "HttpRequestEntity",
            EntityType.HTTP_RESPONSE: "HttpResponseEntity",
            EntityType.DNS_QUERY: "DnsQueryEntity",
            EntityType.NETWORK_ENDPOINT: "NetworkEndpointEntity",
            EntityType.URL: "UrlEntity",
            EntityType.FILE: "FileEntity",
            EntityType.PROCESS: "ProcessEntity",
        }
        return mapping.get(entity_type, "NetworkEndpointEntity")

    def _extract_dns_query_entity(self, query) -> Optional[Dict[str, Any]]:
        """提取DNS查询实体"""
        if not query or not query.hostname:
            return None

        # 生成实体ID
        content = f"{query.hostname}{query.opcode_id or ''}"
        entity_id = self._generate_entity_id("dns_query", content)

        properties = {
            "query_name": query.hostname,  # 匹配EntityService中的匹配规则
            "hostname": query.hostname,  # 保留原有字段
            "query_class": query.class_,
            "opcode": query.opcode,
            "opcode_id": query.opcode_id,
            "packet_uid": query.packet_uid,
            "query_type": query.type,
        }

        return {
            "entity_id": entity_id,
            "entity_type": EntityType.DNS_QUERY,
            "name": f"DNS查询: {query.hostname}",
            "properties": properties,
            "risk_score": self._calculate_dns_query_risk_score(query),
            "description": f"DNS查询: {query.hostname} ({query.type or 'N/A'})",
        }

    def _extract_http_response_entity(self, http_response) -> Optional[Dict[str, Any]]:
        """提取HTTP响应实体"""
        if not http_response:
            return None

        # 生成实体ID
        content = f"{http_response.body or ''}{http_response.code or ''}"
        if http_response.http_headers:
            headers_str = "&".join(
                [f"{h.name}={h.value}" for h in http_response.http_headers]
            )
            content += headers_str
        entity_id = self._generate_entity_id("http_response", content)

        properties = {
            "status_code": http_response.code,
            "body": http_response.body,
            "body_length": http_response.body_length,
            "content_type": http_response.content_type,
            "length": http_response.length,
            "message": http_response.message,
            "status": http_response.status,
        }

        # 添加头部信息
        if http_response.http_headers:
            headers = {h.name: h.value for h in http_response.http_headers}
            properties["headers"] = json.dumps(headers, ensure_ascii=False)

        return {
            "entity_id": entity_id,
            "entity_type": EntityType.HTTP_RESPONSE,
            "name": f"HTTP响应 {http_response.code or 'N/A'}",
            "properties": properties,
            "risk_score": self._calculate_http_response_risk_score(http_response),
            "description": f"HTTP响应: {http_response.code or 'N/A'} {http_response.status or ''}",
        }

    def _extract_user_entity(self, user) -> Optional[Dict[str, Any]]:
        """提取用户实体"""
        if not user or not user.name:
            return None

        # 生成实体ID
        entity_id = self._generate_entity_id("user", user.name)

        properties = {
            "user_name": user.name,
            "user_uid": user.uid,
            "user_type": user.type,
            "user_type_id": user.type_id,
            "has_mfa": user.has_mfa,
        }

        return {
            "entity_id": entity_id,
            "entity_type": EntityType.PROCESS,  # 暂时使用PROCESS类型，后续可以添加USER类型
            "name": user.name,
            "properties": properties,
            "risk_score": self._calculate_user_risk_score(user),
            "description": f"用户: {user.name} ({user.type or 'N/A'})",
        }

    def _calculate_dns_query_risk_score(self, query) -> int:
        """计算DNS查询风险分数"""
        score = 20  # 基础分数

        # 根据查询域名调整
        if query.hostname:
            # 检查是否为可疑域名
            suspicious_keywords = ["malware", "phishing", "exploit", "hack"]
            if any(
                keyword in query.hostname.lower() for keyword in suspicious_keywords
            ):
                score += 50

            # 检查域名长度（过长可能是DGA域名）
            if len(query.hostname) > 50:
                score += 20

        return min(score, 100)

    def _calculate_http_response_risk_score(self, response) -> int:
        """计算HTTP响应风险分数"""
        score = 25  # 基础分数

        # 根据状态码调整
        if response.code:
            if response.code >= 400:
                score += 20  # 错误响应

        # 根据响应内容调整
        if response.body:
            suspicious_content = ["script", "eval", "exploit", "payload"]
            if any(content in response.body.lower() for content in suspicious_content):
                score += 30

        return min(score, 100)

    def _calculate_user_risk_score(self, user) -> int:
        """计算用户风险分数"""
        score = 15  # 基础分数

        # 根据用户类型调整
        if user.type:
            if user.type.lower() in ["admin", "root", "administrator"]:
                score += 25  # 高权限用户

        # 根据MFA状态调整
        if user.has_mfa is False:
            score += 20  # 没有MFA的用户风险更高

        return min(score, 100)
