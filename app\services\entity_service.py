"""
实体管理服务

实现风险实体的创建、查询、更新和关系管理等业务逻辑
"""

from datetime import datetime
from typing import List, Optional
from uuid import uuid4

from app.database.connection import Neo4jConnection
from app.repositories.base import BaseRepository
from app.models.entity import (
    EntityCreate,
    EntityUpdate,
    EntityResponse,
    RelationshipCreate,
    RelationshipResponse,
    EntitySearchParams,
    PathResponse,
)
from app.models.alert import BatchCreateResponse, AlertDetailResponse
import hashlib
import json


class EntityService:
    """实体管理服务"""

    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
        self.repository = BaseRepository(connection)

    def find_or_create_entity(
        self, entity_data: EntityCreate
    ) -> tuple[EntityResponse, bool]:
        """
        查找或创建实体，返回(实体, 是否新创建)

        Args:
            entity_data: 实体创建数据

        Returns:
            tuple: (实体响应, 是否新创建)
        """
        try:
            # 先尝试查找现有实体
            existing_entity = self._find_existing_entity(entity_data)

            if existing_entity:
                # 找到现有实体，更新时间和风险分数
                self._update_entity_on_reuse(existing_entity.entity_id, entity_data)
                return existing_entity, False
            else:
                # 创建新实体
                new_entity = self.create_entity(entity_data)
                return new_entity, True

        except Exception as e:
            raise Exception(f"查找或创建实体失败: {str(e)}")

    def create_entity(self, entity_data: EntityCreate) -> EntityResponse:
        """创建实体"""
        try:
            # 生成唯一ID
            entity_id = str(uuid4())
            current_time = datetime.now()

            # 准备节点属性
            properties = {
                "entity_id": entity_id,
                "name": entity_data.name,
                "risk_score": entity_data.risk_score,
                "description": entity_data.description,
                "created_at": current_time.isoformat(),
                "updated_at": current_time.isoformat(),
                "reuse_count": 0,
                **entity_data.properties,
            }

            # 为特定实体类型添加特殊字段
            if entity_data.entity_type.value == "ProcessEntity":
                if (
                    "process_name" in entity_data.properties
                    and "command_line" in entity_data.properties
                ):
                    process_signature = f"{entity_data.properties['process_name']}|{entity_data.properties['command_line']}"
                    properties["process_signature"] = hashlib.md5(
                        process_signature.encode()
                    ).hexdigest()

            elif entity_data.entity_type.value == "HttpResponseEntity":
                # 为HTTP响应实体添加body_hash字段用于匹配
                if "body" in entity_data.properties and entity_data.properties["body"]:
                    body_hash = hashlib.md5(
                        entity_data.properties["body"].encode()
                    ).hexdigest()
                    properties["body_hash"] = body_hash

            # 创建节点，使用实体类型作为标签
            self.repository.create_node(entity_data.entity_type.value, properties)

            # 返回响应模型
            return EntityResponse(
                entity_id=entity_id,
                entity_type=entity_data.entity_type,
                name=entity_data.name,
                properties=entity_data.properties,
                risk_score=entity_data.risk_score,
                description=entity_data.description,
                created_at=current_time,
                updated_at=current_time,
            )

        except Exception as e:
            raise Exception(f"创建实体失败: {str(e)}")

    def batch_create_entities(
        self, entities: List[EntityCreate]
    ) -> BatchCreateResponse:
        """批量创建实体"""
        try:
            success_count = 0
            failed_count = 0
            created_ids = []
            errors = []

            for i, entity_data in enumerate(entities):
                try:
                    result = self.create_entity(entity_data)
                    success_count += 1
                    created_ids.append(result.entity_id)
                except Exception as e:
                    failed_count += 1
                    errors.append(
                        {
                            "index": i,
                            "entity_type": entity_data.entity_type.value,
                            "error": str(e),
                        }
                    )

            return BatchCreateResponse(
                success_count=success_count,
                failed_count=failed_count,
                created_ids=created_ids,
                errors=errors,
            )

        except Exception as e:
            raise Exception(f"批量创建实体失败: {str(e)}")

    def get_entity_by_id(self, entity_id: str) -> Optional[EntityResponse]:
        """根据ID获取实体"""
        try:
            query = """
            MATCH (e {entity_id: $entity_id})
            RETURN e, labels(e) as entity_labels
            """

            result = self.connection.execute_read_transaction(
                query, {"entity_id": entity_id}
            )

            if not result:
                return None

            node_data = result[0]["e"]
            entity_labels = result[0]["entity_labels"]

            # 找到实体类型标签（排除通用标签）
            entity_type = None
            for label in entity_labels:
                if label in [
                    "HttpRequestEntity",
                    "HttpResponseEntity",
                    "DnsQueryEntity",
                    "NetworkEndpointEntity",
                    "UrlEntity",
                    "FileEntity",
                    "ProcessEntity",
                    "Device",
                ]:
                    entity_type = label
                    break

            if not entity_type:
                raise Exception("未找到有效的实体类型标签")

            return self._convert_to_response(node_data, entity_type)

        except Exception as e:
            raise Exception(f"获取实体失败: {str(e)}")

    def get_entities_by_type(self, entity_type: str) -> List[EntityResponse]:
        """根据类型获取实体列表"""
        try:
            query = f"""
            MATCH (e:{entity_type})
            RETURN e
            ORDER BY e.created_at DESC
            """

            result = self.connection.execute_read_transaction(query)

            return [
                self._convert_to_response(record["e"], entity_type) for record in result
            ]

        except Exception as e:
            raise Exception(f"按类型查询实体失败: {str(e)}")

    def search_entities(
        self, search_params: EntitySearchParams
    ) -> List[EntityResponse]:
        """搜索实体"""
        try:
            # 构建查询条件
            where_clauses = []
            params = {"limit": search_params.limit}

            if search_params.entity_type:
                entity_type = search_params.entity_type.value
                query_start = f"MATCH (e:{entity_type})"
            else:
                query_start = "MATCH (e)"
                entity_type = None

            if search_params.properties:
                for key, value in search_params.properties.items():
                    where_clauses.append(f"e.{key} = ${key}")
                    params[key] = value

            where_clause = ""
            if where_clauses:
                where_clause = "WHERE " + " AND ".join(where_clauses)

            query = f"""
            {query_start}
            {where_clause}
            RETURN e, labels(e) as entity_labels
            ORDER BY e.created_at DESC
            LIMIT $limit
            """

            result = self.connection.execute_read_transaction(query, params)

            entities = []
            for record in result:
                node_data = record["e"]
                entity_labels = record["entity_labels"]

                # 确定实体类型
                if entity_type:
                    current_entity_type = entity_type
                else:
                    current_entity_type = None
                    for label in entity_labels:
                        if label in [
                            "HttpRequestEntity",
                            "HttpResponseEntity",
                            "DnsQueryEntity",
                            "NetworkEndpointEntity",
                            "UrlEntity",
                            "FileEntity",
                            "ProcessEntity",
                            "Device",
                        ]:
                            current_entity_type = label
                            break

                if current_entity_type:
                    entities.append(
                        self._convert_to_response(node_data, current_entity_type)
                    )

            return entities

        except Exception as e:
            raise Exception(f"搜索实体失败: {str(e)}")

    def update_entity(
        self, entity_id: str, update_data: EntityUpdate
    ) -> Optional[EntityResponse]:
        """更新实体"""
        try:
            # 构建动态更新语句
            set_clauses = []
            params = {"entity_id": entity_id, "updated_at": datetime.now().isoformat()}

            # 处理直接字段
            if update_data.name is not None:
                set_clauses.append("e.name = $name")
                params["name"] = update_data.name

            if update_data.risk_score is not None:
                set_clauses.append("e.risk_score = $risk_score")
                params["risk_score"] = update_data.risk_score

            if update_data.description is not None:
                set_clauses.append("e.description = $description")
                params["description"] = update_data.description

            # 处理properties字段
            if update_data.properties:
                for key, value in update_data.properties.items():
                    set_clauses.append(f"e.{key} = ${key}")
                    params[key] = value

            if not set_clauses:
                return self.get_entity_by_id(entity_id)

            query = f"""
            MATCH (e {{entity_id: $entity_id}})
            SET {", ".join(set_clauses)}, e.updated_at = $updated_at
            RETURN e, labels(e) as entity_labels
            """

            result = self.connection.execute_write_transaction(query, params)

            if not result:
                return None

            node_data = result[0]["e"]
            entity_labels = result[0]["entity_labels"]

            # 确定实体类型
            entity_type = None
            for label in entity_labels:
                if label in [
                    "HttpRequestEntity",
                    "HttpResponseEntity",
                    "DnsQueryEntity",
                    "NetworkEndpointEntity",
                    "UrlEntity",
                    "FileEntity",
                    "ProcessEntity",
                    "Device",
                ]:
                    entity_type = label
                    break

            if not entity_type:
                raise Exception("未找到有效的实体类型标签")

            return self._convert_to_response(node_data, entity_type)

        except Exception as e:
            raise Exception(f"更新实体失败: {str(e)}")

    def create_relationship(self, rel_data: RelationshipCreate) -> RelationshipResponse:
        """创建实体关系"""
        try:
            relationship_id = str(uuid4())
            current_time = datetime.now()

            # 准备关系属性
            properties = {
                "relationship_id": relationship_id,
                "created_at": current_time.isoformat(),
                "updated_at": current_time.isoformat(),
            }

            if rel_data.properties:
                properties.update(rel_data.properties)

            # 创建关系
            query = f"""
            MATCH (from_entity {{entity_id: $from_entity_id}})
            MATCH (to_entity {{entity_id: $to_entity_id}})
            CREATE (from_entity)-[r:{rel_data.relationship_type}]->(to_entity)
            SET r += $properties
            RETURN r
            """

            result = self.connection.execute_write_transaction(
                query,
                {
                    "from_entity_id": rel_data.from_entity_id,
                    "to_entity_id": rel_data.to_entity_id,
                    "properties": properties,
                },
            )

            if not result:
                raise Exception("创建关系失败，实体不存在")

            return RelationshipResponse(
                relationship_id=relationship_id,
                relationship_type=rel_data.relationship_type,
                from_entity_id=rel_data.from_entity_id,
                to_entity_id=rel_data.to_entity_id,
                properties=rel_data.properties,
                created_at=current_time,
                updated_at=current_time,
            )

        except Exception as e:
            raise Exception(f"创建实体关系失败: {str(e)}")

    def get_related_alerts(self, entity_id: str) -> List[AlertDetailResponse]:
        """获取实体关联的告警"""
        try:
            query = """
            MATCH (e {entity_id: $entity_id})-[:LINKED_TO]-(a:AlertDetail)
            RETURN a
            ORDER BY a.start_time DESC
            """

            result = self.connection.execute_read_transaction(
                query, {"entity_id": entity_id}
            )

            # 这里简化返回，实际应该转换为AlertDetailResponse
            # 需要导入AlertService或在此处进行转换
            return []

        except Exception as e:
            raise Exception(f"获取实体关联告警失败: {str(e)}")

    def get_entity_relationships(self, entity_id: str) -> List[RelationshipResponse]:
        """获取实体的关系"""
        try:
            query = """
            MATCH (e {entity_id: $entity_id})-[r]-(other)
            RETURN r, type(r) as rel_type, 
                   startNode(r).entity_id as from_id,
                   endNode(r).entity_id as to_id
            """

            result = self.connection.execute_read_transaction(
                query, {"entity_id": entity_id}
            )

            relationships = []
            for record in result:
                rel_data = record["r"]
                rel_type = record["rel_type"]
                from_id = record["from_id"]
                to_id = record["to_id"]

                relationships.append(
                    RelationshipResponse(
                        relationship_id=rel_data.get("relationship_id", ""),
                        relationship_type=rel_type,
                        from_entity_id=from_id,
                        to_entity_id=to_id,
                        properties=dict(rel_data) if rel_data else {},
                        created_at=datetime.fromisoformat(
                            rel_data.get("created_at", datetime.now().isoformat())
                        ),
                        updated_at=datetime.fromisoformat(
                            rel_data.get("updated_at", datetime.now().isoformat())
                        ),
                    )
                )

            return relationships

        except Exception as e:
            raise Exception(f"获取实体关系失败: {str(e)}")

    def find_entity_path(
        self, from_entity_id: str, to_entity_id: str, max_depth: int = 5
    ) -> List[PathResponse]:
        """查找实体间路径"""
        try:
            query = f"""
            MATCH path = (from_entity {{entity_id: $from_entity_id}})-[*1..{max_depth}]-(to_entity {{entity_id: $to_entity_id}})
            RETURN path
            ORDER BY length(path)
            LIMIT 10
            """

            result = self.connection.execute_read_transaction(
                query, {"from_entity_id": from_entity_id, "to_entity_id": to_entity_id}
            )

            paths = []
            for record in result:
                path = record["path"]
                nodes = path.nodes
                relationships = path.relationships

                # 转换节点和关系（简化版）
                path_nodes = []
                path_relationships = []

                for node in nodes:
                    # 简化的节点转换
                    path_nodes.append(
                        {"entity_id": node.get("entity_id"), "properties": dict(node)}
                    )

                for rel in relationships:
                    # 简化的关系转换
                    path_relationships.append(
                        {"relationship_type": rel.type, "properties": dict(rel)}
                    )

                paths.append(
                    PathResponse(
                        path_length=len(relationships),
                        nodes=path_nodes,
                        relationships=path_relationships,
                    )
                )

            return paths

        except Exception as e:
            raise Exception(f"查找实体路径失败: {str(e)}")

    def _convert_to_response(self, node_data: dict, entity_type: str) -> EntityResponse:
        """将Neo4j节点数据转换为响应模型"""
        # 提取基础属性
        entity_id = node_data.get("entity_id")
        name = node_data.get("name")
        risk_score = node_data.get("risk_score", 0)
        description = node_data.get("description")
        created_at = node_data.get("created_at")
        updated_at = node_data.get("updated_at")

        # 提取实体属性（排除基础字段）
        properties = {
            k: v
            for k, v in node_data.items()
            if k
            not in [
                "entity_id",
                "name",
                "risk_score",
                "description",
                "created_at",
                "updated_at",
            ]
        }

        return EntityResponse(
            entity_id=entity_id,
            entity_type=entity_type,
            name=name,
            properties=properties,
            risk_score=risk_score,
            description=description,
            created_at=datetime.fromisoformat(created_at)
            if created_at
            else datetime.now(),
            updated_at=datetime.fromisoformat(updated_at)
            if updated_at
            else datetime.now(),
        )

    def _find_existing_entity(
        self, entity_data: EntityCreate
    ) -> Optional[EntityResponse]:
        """
        根据实体类型查找现有的匹配实体

        Args:
            entity_data: 实体数据

        Returns:
            现有实体或None
        """
        try:
            entity_type = entity_data.entity_type.value
            match_criteria = self._get_match_criteria(entity_data)

            if not match_criteria:
                return None

            # 特殊处理：为HTTP响应实体添加body_hash
            if entity_type == "HttpResponseEntity" and "body" in match_criteria:
                body_hash = hashlib.md5(match_criteria["body"].encode()).hexdigest()
                # 先尝试查找是否已有body_hash字段的实体
                existing_with_hash = self._find_entity_with_body_hash(
                    entity_type, match_criteria, body_hash
                )
                if existing_with_hash:
                    return existing_with_hash
                # 如果没有，继续使用原始匹配条件

            # 构建查询条件
            where_clauses = []
            params = {}

            for key, value in match_criteria.items():
                if value is None:
                    # 处理None值
                    where_clauses.append(f"e.{key} IS NULL")
                elif isinstance(value, (int, float)):
                    # 数值类型直接匹配
                    where_clauses.append(f"e.{key} = ${key}")
                    params[key] = value
                elif isinstance(value, str):
                    # 字符串类型，处理空字符串
                    if value.strip():
                        where_clauses.append(f"e.{key} = ${key}")
                        params[key] = value
                    else:
                        where_clauses.append(f"(e.{key} = ${key} OR e.{key} IS NULL)")
                        params[key] = value
                else:
                    # 其他类型转换为字符串
                    where_clauses.append(f"e.{key} = ${key}")
                    params[key] = str(value)

            if not where_clauses:
                return None

            where_clause = " AND ".join(where_clauses)

            # 查询处理：添加排序以获取最新的匹配实体
            query = f"""
            MATCH (e:{entity_type})
            WHERE {where_clause}
            RETURN e, labels(e) as entity_labels
            ORDER BY e.updated_at DESC, e.created_at DESC
            LIMIT 1
            """

            result = self.connection.execute_read_transaction(query, params)

            if result:
                node_data = result[0]["e"]
                return self._convert_to_response(node_data, entity_type)

            return None

        except Exception as e:
            # 记录详细错误信息用于调试
            error_msg = f"查找现有实体失败: {str(e)}, 实体类型: {entity_data.entity_type.value}, 匹配条件: {match_criteria if 'match_criteria' in locals() else 'N/A'}"
            raise Exception(error_msg)

    def _find_entity_with_body_hash(
        self, entity_type: str, match_criteria: dict, body_hash: str
    ) -> Optional[EntityResponse]:
        """
        为HTTP响应实体查找具有body_hash的匹配实体

        这是一个特殊处理方法，用于处理HTTP响应实体的body哈希匹配
        """
        try:
            # 构建包含body_hash的查询
            hash_criteria = {k: v for k, v in match_criteria.items() if k != "body"}
            hash_criteria["body_hash"] = body_hash

            where_clauses = []
            params = {}

            for key, value in hash_criteria.items():
                where_clauses.append(f"e.{key} = ${key}")
                params[key] = value

            where_clause = " AND ".join(where_clauses)

            query = f"""
            MATCH (e:{entity_type})
            WHERE {where_clause}
            RETURN e, labels(e) as entity_labels
            ORDER BY e.updated_at DESC
            LIMIT 1
            """

            result = self.connection.execute_read_transaction(query, params)

            if result:
                node_data = result[0]["e"]
                return self._convert_to_response(node_data, entity_type)

            return None

        except Exception:
            # 如果哈希查询失败，返回None让主查询继续
            return None

    def _get_match_criteria(self, entity_data: EntityCreate) -> dict:
        """
        根据实体类型生成匹配条件

        设计原则：
        1. 共性优先：匹配多个告警的共性特征
        2. 分层匹配：主要字段优先，辅助字段补充
        3. 跨设备兼容：考虑不同安全设备的数据格式差异
        4. 容错处理：关键字段缺失时使用备选方案

        Args:
            entity_data: 实体数据

        Returns:
            匹配条件字典，空字典表示无法匹配
        """
        entity_type = entity_data.entity_type.value
        properties = entity_data.properties

        if entity_type == "NetworkEndpointEntity":
            return self._get_network_endpoint_criteria(properties)

        elif entity_type == "FileEntity":
            return self._get_file_entity_criteria(properties)

        elif entity_type == "ProcessEntity":
            return self._get_process_entity_criteria(properties)

        elif entity_type == "HttpRequestEntity":
            return self._get_http_request_criteria(properties)

        elif entity_type == "UrlEntity":
            return self._get_url_entity_criteria(properties)

        elif entity_type == "DnsQueryEntity":
            return self._get_dns_query_criteria(properties)

        elif entity_type == "HttpResponseEntity":
            return self._get_http_response_criteria(properties)

        return {}

    def _get_network_endpoint_criteria(self, properties: dict) -> dict:
        """
        网络端点实体匹配策略

        主要匹配字段：ip_address + port
        辅助匹配字段：ip_address + endpoint_type (当端口缺失时)
        备选匹配字段：仅ip_address (当端口和类型都缺失时)

        跨设备兼容性：
        - WAF设备：通常有完整的IP+端口信息
        - IDS设备：可能只有IP信息
        - EDR设备：可能有endpoint_type区分
        """
        criteria = {}

        # 必须有IP地址才能匹配
        if not properties.get("ip_address"):
            return {}

        criteria["ip_address"] = properties["ip_address"]

        # 优先使用IP+端口进行匹配
        if properties.get("port") is not None:
            criteria["port"] = properties["port"]
        # 如果没有端口但有endpoint_type，使用IP+类型匹配
        elif properties.get("endpoint_type"):
            criteria["endpoint_type"] = properties["endpoint_type"]

        return criteria

    def _get_file_entity_criteria(self, properties: dict) -> dict:
        """
        文件实体匹配策略

        主要匹配字段：file_hash (任何哈希算法)
        辅助匹配字段：filename + file_size (当哈希缺失时)
        备选匹配字段：file_path (当文件名也缺失时)

        跨设备兼容性：
        - EDR设备：通常提供完整哈希信息
        - 沙箱设备：可能只有文件名和大小
        - 网络设备：可能只有文件路径信息
        """
        # 优先级1：任何类型的文件哈希
        hash_fields = ["file_hash", "hash_md5", "hash_sha1", "hash_sha256"]
        for hash_field in hash_fields:
            if properties.get(hash_field):
                return {hash_field: properties[hash_field]}

        # 优先级2：文件名+文件大小组合匹配
        if properties.get("filename") and properties.get("file_size"):
            return {
                "filename": properties["filename"],
                "file_size": properties["file_size"],
            }

        # 优先级3：仅文件名匹配（风险较高，但必要时使用）
        if properties.get("filename"):
            return {"filename": properties["filename"]}

        # 优先级4：文件路径匹配（最后备选）
        if properties.get("file_path"):
            return {"file_path": properties["file_path"]}

        return {}

    def _get_process_entity_criteria(self, properties: dict) -> dict:
        """
        进程实体匹配策略

        主要匹配字段：process_signature (进程名+命令行的哈希)
        辅助匹配字段：process_name + process_path
        备选匹配字段：仅process_name

        跨设备兼容性：
        - EDR设备：提供完整的进程信息
        - 系统日志：可能只有进程名
        - 网络设备：可能从User-Agent等推断进程
        """
        # 优先级1：进程签名匹配
        if properties.get("process_name") and properties.get("command_line"):
            process_signature = (
                f"{properties['process_name']}|{properties['command_line']}"
            )
            process_hash = hashlib.md5(process_signature.encode()).hexdigest()
            return {"process_signature": process_hash}

        # 优先级2：进程名+路径匹配
        if properties.get("process_name") and properties.get("process_path"):
            return {
                "process_name": properties["process_name"],
                "process_path": properties["process_path"],
            }

        # 优先级3：仅进程名匹配
        if properties.get("process_name"):
            return {"process_name": properties["process_name"]}

        return {}

    def _get_http_request_criteria(self, properties: dict) -> dict:
        """
        HTTP请求实体匹配策略

        主要匹配字段：method + url + user_agent
        辅助匹配字段：method + url (当User-Agent缺失时)
        备选匹配字段：仅url (当方法缺失时)

        跨设备兼容性：
        - WAF设备：提供完整的HTTP请求信息
        - 代理设备：可能缺少某些头部信息
        - IDS设备：可能只有URL信息
        """
        # 优先级1：方法+URL+User-Agent三元组匹配
        if (
            properties.get("method")
            and properties.get("url")
            and properties.get("user_agent")
        ):
            return {
                "method": properties["method"],
                "url": properties["url"],
                "user_agent": properties["user_agent"],
            }

        # 优先级2：方法+URL匹配
        if properties.get("method") and properties.get("url"):
            return {"method": properties["method"], "url": properties["url"]}

        # 优先级3：仅URL匹配（风险较高，但对于GET请求可能合理）
        if properties.get("url"):
            return {"url": properties["url"]}

        return {}

    def _get_url_entity_criteria(self, properties: dict) -> dict:
        """
        URL实体匹配策略

        主要匹配字段：full_url
        辅助匹配字段：hostname + path (当完整URL缺失时)
        备选匹配字段：仅hostname (当路径缺失时)

        跨设备兼容性：
        - 网络设备：通常提供完整URL
        - DNS设备：可能只有hostname
        - 代理设备：可能分别记录各个组件
        """
        # 优先级1：完整URL匹配
        if properties.get("full_url"):
            return {"full_url": properties["full_url"]}

        # 优先级2：URL字段匹配（兼容性考虑）
        if properties.get("url"):
            return {"url": properties["url"]}

        # 优先级3：hostname+path组合匹配
        if properties.get("hostname") and properties.get("path"):
            return {"hostname": properties["hostname"], "path": properties["path"]}

        # 优先级4：仅hostname匹配
        if properties.get("hostname"):
            return {"hostname": properties["hostname"]}

        return {}

    def _get_dns_query_criteria(self, properties: dict) -> dict:
        """
        DNS查询实体匹配策略

        主要匹配字段：query_name + query_type
        辅助匹配字段：仅query_name (当查询类型缺失时)
        备选匹配字段：hostname (兼容性字段)

        跨设备兼容性：
        - DNS服务器：提供完整查询信息
        - 网络监控：可能只有域名信息
        - 代理设备：可能使用不同字段名
        """
        # 优先级1：查询名+查询类型匹配
        if properties.get("query_name") and properties.get("query_type"):
            return {
                "query_name": properties["query_name"],
                "query_type": properties["query_type"],
            }

        # 优先级2：仅查询名匹配
        if properties.get("query_name"):
            return {"query_name": properties["query_name"]}

        # 优先级3：hostname字段匹配（兼容性）
        if properties.get("hostname"):
            return {"hostname": properties["hostname"]}

        return {}

    def _get_http_response_criteria(self, properties: dict) -> dict:
        """
        HTTP响应实体匹配策略

        主要匹配字段：status_code + content_type + body_hash
        辅助匹配字段：status_code + content_type
        备选匹配字段：仅status_code

        注意：HTTP响应的匹配相对复杂，因为相同的响应可能出现在不同的请求中
        """
        # 优先级1：状态码+内容类型+响应体哈希（如果有）
        if (
            properties.get("status_code")
            and properties.get("content_type")
            and properties.get("body")
        ):
            # 为响应体生成哈希用于匹配
            body_hash = hashlib.md5(properties["body"].encode()).hexdigest()
            return {
                "status_code": properties["status_code"],
                "content_type": properties["content_type"],
                "body_hash": body_hash,
            }

        # 优先级2：状态码+内容类型匹配
        if properties.get("status_code") and properties.get("content_type"):
            return {
                "status_code": properties["status_code"],
                "content_type": properties["content_type"],
            }

        # 优先级3：仅状态码匹配（风险很高，仅在必要时使用）
        if properties.get("status_code"):
            return {"status_code": properties["status_code"]}

        return {}

    def _update_entity_on_reuse(self, entity_id: str, entity_data: EntityCreate):
        """
        当实体被重用时更新其信息

        Args:
            entity_id: 实体ID
            entity_data: 新的实体数据
        """
        try:
            current_time = datetime.now()

            # 更新时间和可能的风险分数
            update_props = {
                "updated_at": current_time.isoformat(),
                "reuse_count": "COALESCE(e.reuse_count, 0) + 1",
            }

            # 如果新的风险分数更高，则更新
            if entity_data.risk_score > 0:
                update_props["risk_score"] = (
                    f"CASE WHEN e.risk_score < {entity_data.risk_score} THEN {entity_data.risk_score} ELSE e.risk_score END"
                )

            # 构建更新查询
            set_clauses = []
            for key, value in update_props.items():
                if key == "reuse_count" or key.startswith("risk_score"):
                    set_clauses.append(f"e.{key} = {value}")
                else:
                    set_clauses.append(f"e.{key} = '{value}'")

            set_clause = ", ".join(set_clauses)

            query = f"""
            MATCH (e {{entity_id: $entity_id}})
            SET {set_clause}
            RETURN e
            """

            self.connection.execute_write_transaction(query, {"entity_id": entity_id})

        except Exception as e:
            raise Exception(f"更新实体重用信息失败: {str(e)}")

    def create_evidence_entity_relationship(
        self, evidence_id: str, entity_id: str, evidence_context: dict
    ) -> str:
        """
        创建证据与实体的关系，记录特征信息

        Args:
            evidence_id: 证据ID
            entity_id: 实体ID
            evidence_context: 证据上下文信息

        Returns:
            关系ID
        """
        try:
            relationship_id = str(uuid4())
            current_time = datetime.now()

            # 准备关系属性，包含证据特征
            rel_properties = {
                "relationship_id": relationship_id,
                "created_at": current_time.isoformat(),
                "evidence_context": json.dumps(evidence_context, ensure_ascii=False),
                "relationship_type": "EVIDENCE_ENTITY_RELATION",
            }

            # 创建关系
            query = """
            MATCH (evidence {evidence_id: $evidence_id})
            MATCH (entity {entity_id: $entity_id})
            CREATE (evidence)-[r:RELATES_TO $rel_props]->(entity)
            RETURN r
            """

            self.connection.execute_write_transaction(
                query,
                {
                    "evidence_id": evidence_id,
                    "entity_id": entity_id,
                    "rel_props": rel_properties,
                },
            )

            return relationship_id

        except Exception as e:
            raise Exception(f"创建证据实体关系失败: {str(e)}")
