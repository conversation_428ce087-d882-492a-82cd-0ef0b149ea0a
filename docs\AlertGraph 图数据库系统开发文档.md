# AlertGraph 图数据库系统开发文档

## 1. 项目概述

### 1.1 项目简介

AlertGraph是一个基于Neo4j图数据库的安全告警知识图谱系统，用于存储、管理和查询安全告警相关数据，支持三层告警逻辑（告警细节、告警会话、告警事件）的知识图谱构建。

### 1.2 技术栈

- **Python**: 3.12
- **Web框架**: FastAPI 0.115.12
- **图数据库**: Neo4j 5.26.7-community

### 1.3 核心功能

- 告警数据的批量和流式写入
- 多层级告警关系构建
- 风险实体关联分析
- **智能实体去重机制**
- 研判记录管理
- 威胁情报集成
- RESTful API接口服务

## 2. 系统架构设计

### 2.1 核心组件设计

### 2.1.1 数据库连接层

负责Neo4j数据库连接管理、连接池维护、事务处理等基础功能。

### 2.1.2 数据模型层

定义图数据库中的节点和关系模型，包括告警细节、研判记录、风险实体等。

### 2.1.3 数据访问层（Repository Pattern）

封装对Neo4j的CRUD操作，提供面向对象的数据访问接口。

### 2.1.4 业务逻辑层

实现核心业务逻辑，包括告警关联、研判处理、图谱构建等。

### 2.1.5 API层

提供RESTful API接口，支持前端和外部系统调用。

## 3. 数据模型设计

### 3.1 节点类型定义

### 3.1.1 告警细节节点 (AlertDetail)

```python
# 节点标签: AlertDetail
# 基于 app/models/alert.py 中的 AlertDetailBase 模型
Properties:
- vid: STRING (图数据库节点唯一ID，系统自动生成)
- alert_detail_id: STRING (告警细节ID，必填)
- alert_name: STRING (告警名称，必填)
- alert_message: STRING (告警描述，必填)
- vulnerabilities: STRING (漏洞描述，可选)
- product_name: STRING (设备名称，必填)
- time: TIMESTAMP (告警时间，系统自动生成)
- confidence_score: INT (置信度分数 0-100，必填)
- impact: STRING (影响等级: Critical/High/Medium/Low/Unknown，必填)
- risk: STRING (风险等级: Critical/High/Medium/Low/Unknown，必填)
- severity: STRING (严重性: Fatal/Critical/High/Medium/Low/Unknown，必填)
- status: STRING (处理状态: New/InProgress/Suppressed/Resolved/Archived/Unknown，默认New)
- comment: STRING (评论，可选)
- raw_data: STRING (原始推送数据，可选)
- created_at: TIMESTAMP (创建时间，系统自动生成)
- updated_at: TIMESTAMP (更新时间，系统自动生成)
```

**实际实现参考**:
```python
# app/models/alert.py - AlertDetailBase
class AlertDetailBase(BaseModel):
    alert_detail_id: str = Field(..., description="告警细节ID")
    alert_name: str = Field(..., description="告警名称")
    alert_message: str = Field(..., description="告警描述")
    vulnerabilities: Optional[str] = Field(None, description="漏洞描述")
    product_name: str = Field(..., description="设备名称")
    confidence_score: int = Field(..., ge=0, le=100, description="置信度分数 0-100")
    impact: Impact = Field(..., description="影响等级")
    risk: Risk = Field(..., description="风险等级")
    severity: Severity = Field(..., description="严重性")
    status: AlertStatus = Field(AlertStatus.NEW, description="处理状态")
    comment: Optional[str] = Field(None, description="评论")
    raw_data: Optional[str] = Field(None, description="原始推送数据")
```

### 3.1.2 研判记录节点 (Verdict)

```python
# 节点标签: Verdict
# 基于 app/models/alert.py 中的 VerdictBase 模型
Properties:
- verdict_id: STRING (研判记录唯一ID，系统自动生成)
- type_id: INT (研判类型: 1-AI, 2-人工，必填)
- type: STRING (研判类型说明: AI研判/人工研判，必填)
- label: INT (研判标签: 1-误报, 2-确认攻击, 3-可疑, 4-数据不足，必填)
- reason: STRING (研判理由，必填)
- commiter: STRING (研判提交者，必填)
- time: TIMESTAMP (研判时间，系统自动生成)
- comment: STRING (研判评论，可选)
- created_at: TIMESTAMP (创建时间，系统自动生成)
- updated_at: TIMESTAMP (更新时间，系统自动生成)
```

**实际实现参考**:
```python
# app/models/alert.py - VerdictBase
class VerdictBase(BaseModel):
    type_id: VerdictType = Field(..., description="研判类型: 1-AI, 2-人工")
    type: str = Field(..., description="研判类型说明: AI研判/人工研判")
    label: VerdictLabel = Field(..., description="研判标签: 1-误报, 2-确认攻击, 3-可疑, 4-数据不足")
    reason: str = Field(..., description="研判理由")
    commiter: str = Field(..., description="研判提交者")
    comment: Optional[str] = Field(None, description="研判评论")
```

### 3.1.3 证据信息节点 (Evidence)

```python
# 节点标签: Evidence
# 基于 app/services/evidence_service.py 中的实际实现
Properties:
- evidence_id: STRING (证据唯一ID，基于内容生成的UUID5)
- evidence_type: STRING (证据类型，如"combined_evidence")
- evidence_types: LIST[STRING] (所有证据制品的类型列表)
- is_alert_trigger: BOOLEAN (是否包含告警触发证据)
- include_payload: BOOLEAN (是否包含攻击载荷)
- evidence_count: INT (包含的证据制品数量)
- raw_evidence_data: STRING (原始证据数据JSON，包含所有evidence_artifacts)
- created_time: TIMESTAMP (创建时间，系统自动生成)
- updated_time: TIMESTAMP (更新时间，系统自动生成)

# 动态属性（根据证据内容添加）:
- http_methods: LIST[STRING] (HTTP方法列表，如果包含HTTP请求)
- domains: LIST[STRING] (域名列表，如果包含URL或DNS查询)
- ip_addresses: LIST[STRING] (IP地址列表，如果包含网络端点)
- file_hashes: LIST[STRING] (文件哈希列表，如果包含文件信息)
- process_names: LIST[STRING] (进程名称列表，如果包含进程信息)
```

**实际实现参考**:
```python
# app/services/evidence_service.py - _create_evidence_node
properties = {
    "evidence_id": evidence_id,
    "evidence_type": "combined_evidence",
    "evidence_types": evidence_types,
    "is_alert_trigger": has_trigger,
    "include_payload": has_payload,
    "evidence_count": len(evidence_artifacts),
    "raw_evidence_data": json.dumps(all_raw_data, ensure_ascii=False),
    "created_time": current_time.isoformat(),
    "updated_time": current_time.isoformat()
}
```

### 3.1.4 设备信息节点 (Device)

```python
# 节点标签: Device
# 基于 app/models/device.py 中的 DeviceBase 模型
Properties:
- device_id: STRING (设备唯一标识，必填)
- uuid: STRING (设备UUID，可选)
- org: STRING (所属组织，可选)
- ip: STRING (主要IP地址，可选)
- hostname: STRING (主机名，可选)
- mac: STRING (MAC地址，可选)
- os_name: STRING (操作系统名称，可选)
- count: INT (告警数量统计，默认1)
- created_at: TIMESTAMP (创建时间，系统自动生成)
- updated_at: TIMESTAMP (更新时间，系统自动生成)
```

**实际实现参考**:
```python
# app/models/device.py - DeviceBase
class DeviceBase(BaseModel):
    device_id: str = Field(..., description="设备唯一标识")
    uuid: Optional[str] = Field(None, description="设备UUID")
    org: Optional[str] = Field(None, description="所属组织")
    ip: Optional[str] = Field(None, description="主要IP地址")
    hostname: Optional[str] = Field(None, description="主机名")
    mac: Optional[str] = Field(None, description="MAC地址")
    os_name: Optional[str] = Field(None, description="操作系统名称")
    count: Optional[int] = Field(1, description="告警数量统计")
```

**设备ID生成逻辑**:
```python
# app/services/device_service.py - _generate_device_id
# 基于主要标识字段生成设备ID，优先级：hostname > ip > product_name
```

### 3.1.5 风险实体节点

基于 `app/models/base.py` 中的 `EntityType` 枚举和 `app/services/evidence_service.py` 中的实际提取逻辑：

```python
# 支持的实体类型
class EntityType(str, Enum):
    HTTP_REQUEST = "HttpRequestEntity"
    HTTP_RESPONSE = "HttpResponseEntity"
    DNS_QUERY = "DnsQueryEntity"
    NETWORK_ENDPOINT = "NetworkEndpointEntity"
    URL = "UrlEntity"
    FILE = "FileEntity"
    PROCESS = "ProcessEntity"
    DEVICE = "Device"
```

#### ******* 网络端点实体 (NetworkEndpointEntity)
```python
# 节点标签: NetworkEndpointEntity
# 基于 app/services/evidence_service.py 中的 _extract_network_endpoint_entity
Properties:
- entity_id: STRING (实体唯一ID)
- name: STRING (实体名称，通常为IP地址)
- ip_address: STRING (IP地址，主要匹配字段)
- port: INT (端口号，辅助匹配字段)
- hostname: STRING (主机名)
- mac: STRING (MAC地址)
- domain: STRING (域名)
- endpoint_type: STRING (端点类型: src/dst)
- location: OBJECT (地理位置信息，包含country, city, lat, long)
- autonomous_system: OBJECT (AS信息)
- operating_system: OBJECT (操作系统信息)
- reuse_count: INT (复用次数，默认1)
- risk_score: INT (风险分数，0-100)
- description: STRING (实体描述)
- created_at: TIMESTAMP (创建时间)
- updated_at: TIMESTAMP (更新时间)
```

**提取逻辑参考**:
```python
# app/services/evidence_service.py - _extract_network_endpoint_entity
def _extract_network_endpoint_entity(self, endpoint: NetworkEndpointModel, endpoint_type: str) -> Dict[str, Any]:
    if not endpoint.ip:
        return None

    return {
        "entity_type": EntityType.NETWORK_ENDPOINT,
        "name": f"{endpoint.ip}:{endpoint.port}" if endpoint.port else endpoint.ip,
        "properties": {
            "ip_address": endpoint.ip,
            "port": endpoint.port,
            "hostname": endpoint.hostname,
            "mac": endpoint.mac,
            "domain": endpoint.domain,
            "endpoint_type": endpoint_type,
            "location": endpoint.location.model_dump() if endpoint.location else None,
            # ... 其他属性
        },
        "risk_score": self._calculate_endpoint_risk_score(endpoint),
        "description": f"{endpoint_type}端点: {endpoint.ip}"
    }
```

#### ******* 文件实体 (FileEntity)
```python
# 节点标签: FileEntity
# 基于 app/services/evidence_service.py 中的 _extract_file_entity
Properties:
- entity_id: STRING (实体唯一ID)
- name: STRING (实体名称，通常为文件名)
- file_hash: STRING (文件哈希，主要匹配字段)
- filename: STRING (文件名，备选匹配字段)
- file_path: STRING (文件路径)
- file_size: INT (文件大小)
- file_extension: STRING (文件扩展名)
- file_type: STRING (文件类型)
- mime_type: STRING (MIME类型)
- confidentiality: STRING (机密性)
- confidentiality_id: INT (机密性ID)
- accessed_time: TIMESTAMP (文件访问时间)
- created_time: TIMESTAMP (文件创建时间)
- modified_time: TIMESTAMP (文件修改时间)
- hash_md5: STRING (MD5哈希)
- hash_sha1: STRING (SHA1哈希)
- hash_sha256: STRING (SHA256哈希)
- reuse_count: INT (复用次数，默认1)
- risk_score: INT (风险分数，0-100)
- description: STRING (实体描述)
- created_at: TIMESTAMP (实体创建时间)
- updated_at: TIMESTAMP (实体更新时间)
```

**提取逻辑参考**:
```python
# app/services/evidence_service.py - _extract_file_entity
def _extract_file_entity(self, file: FileModel) -> Dict[str, Any]:
    if not file.name and not file.path:
        return None

    # 提取文件哈希（优先SHA256, SHA1, MD5）
    file_hash = None
    if file.hashes:
        for hash_obj in file.hashes:
            if hash_obj.algorithm.upper() == "SHA256":
                file_hash = hash_obj.value
                break
        # 如果没有SHA256，尝试其他哈希
        if not file_hash:
            file_hash = file.hashes[0].value

    return {
        "entity_type": EntityType.FILE,
        "name": file.name or os.path.basename(file.path or ""),
        "properties": {
            "file_hash": file_hash,
            "filename": file.name,
            "file_path": file.path,
            "file_size": getattr(file, 'size', None),
            "file_extension": file.ext,
            "file_type": file.type,
            # ... 其他属性
        },
        "risk_score": self._calculate_file_risk_score(file),
        "description": f"文件: {file.name or file.path}"
    }
```

#### ******* 进程实体 (ProcessEntity)
```python
# 节点标签: ProcessEntity
Properties:
- process_signature: STRING (进程签名，主要匹配字段)
- process_name: STRING (进程名称，备选匹配字段)
- process_path: STRING (进程路径)
- process_id: INT (进程ID)
- command_line: STRING (命令行)
- user_name: STRING (用户名)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* URL实体 (UrlEntity)
```python
# 节点标签: UrlEntity
Properties:
- full_url: STRING (完整URL，主要匹配字段)
- url: STRING (URL路径)
- domain: STRING (域名)
- hostname: STRING (主机名)
- path: STRING (路径)
- port: INT (端口)
- scheme: STRING (协议)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* DNS查询实体 (DnsQueryEntity)
```python
# 节点标签: DnsQueryEntity
Properties:
- query_name: STRING (查询域名，主要匹配字段)
- query_type: STRING (查询类型，辅助匹配字段)
- hostname: STRING (主机名)
- query_class: STRING (查询类)
- opcode: STRING (操作码)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* HTTP请求实体 (HttpRequestEntity)
```python
# 节点标签: HttpRequestEntity
Properties:
- method: STRING (HTTP方法，主要匹配字段)
- url: STRING (请求URL，主要匹配字段)
- user_agent: STRING (用户代理，主要匹配字段)
- body: STRING (请求体)
- version: STRING (HTTP版本)
- hostname: STRING (主机名)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### 3.2 关系类型定义

基于 `app/core/constants.py` 中的 `RelationshipTypes` 类和实际代码实现：

```python
# 已实现的关系类型
class RelationshipTypes:
    HAS_VERDICT = "HAS_VERDICT"            # 告警细节->研判记录
    HAS_EVIDENCE = "HAS_EVIDENCE"          # 告警细节->证据信息
    RELATES_TO = "RELATES_TO"              # 证据信息->风险实体
    GENERATED_BY = "GENERATED_BY"          # 告警细节->设备信息
    BELONGS_TO = "BELONGS_TO"              # 告警细节->告警会话 (未实现)
    CONTAINS = "CONTAINS"                  # 告警会话->告警事件 (未实现)
```

#### 3.2.1 HAS_EVIDENCE 关系 (已实现)
**方向**: AlertDetail -> Evidence
**用途**: 关联告警细节与其证据信息
**属性**:
```python
Properties:
- created_time: TIMESTAMP (关系创建时间)
- is_trigger: BOOLEAN (证据是否为告警触发原因)
- has_payload: BOOLEAN (证据是否包含攻击载荷)
- evidence_count: INT (证据制品数量)
```

**实现位置**: `app/services/evidence_service.py:103-115`

#### 3.2.2 RELATES_TO 关系 (已实现)
**方向**: Evidence -> Entity (各种风险实体)
**用途**: 关联证据信息与从中提取的风险实体
**属性**:
```python
Properties:
- entity_type: STRING (实体类型)
- created_time: TIMESTAMP (关系创建时间)
- extraction_reason: STRING (提取原因，固定为"从证据中自动提取")
- is_new_entity: BOOLEAN (实体是否为新创建)
- entity_context: STRING (实体上下文信息JSON，包含实体快照)
```

**实现位置**: `app/services/evidence_service.py:442-471`

#### 3.2.3 GENERATED_BY 关系 (已实现)
**方向**: AlertDetail -> Device
**用途**: 关联告警细节与生成该告警的设备
**属性**:
```python
Properties:
- created_time: TIMESTAMP (关系创建时间)
- relationship_reason: STRING (关系原因，固定为"告警由此设备生成")
```

**实现位置**: `app/services/device_service.py:48-69`

#### 3.2.4 HAS_VERDICT 关系 (已实现)
**方向**: AlertDetail -> Verdict
**用途**: 关联告警细节与研判记录
**属性**:
```python
Properties:
- created_time: TIMESTAMP (关系创建时间)
```

**实现位置**: `app/services/verdict_service.py:69-98`

## 4. 核心接口设计

### 4.1 数据库连接接口

### 4.1.1 连接管理器

```python
class Neo4jConnection:
    """Neo4j连接管理器"""

    def __init__(self, uri: str, user: str, password: str)
    def get_driver(self) -> Driver
    def close(self)
    def execute_query(self, query: str, parameters: dict = None) -> List[Record]
    def execute_write_transaction(self, query: str, parameters: dict = None) -> List[Record]
    def execute_read_transaction(self, query: str, parameters: dict = None) -> List[Record]

```

### 4.1.2 基础仓储类

```python
class BaseRepository:
    """基础仓储类，提供通用CRUD操作"""

    def __init__(self, connection: Neo4jConnection)
    def create_node(self, label: str, properties: dict) -> str
    def get_node_by_id(self, node_id: str) -> Optional[dict]
    def update_node(self, node_id: str, properties: dict) -> bool
    def delete_node(self, node_id: str) -> bool
    def create_relationship(self, from_id: str, to_id: str, rel_type: str, properties: dict = None) -> bool
    def batch_create_nodes(self, nodes: List[dict]) -> List[str]

```

### 4.2 业务接口设计

### 4.2.1 告警管理接口

```python
class AlertService:
    """告警管理服务"""

    # 数据写入
    def create_alert_detail(self, alert_data: AlertDetailCreate) -> AlertDetailResponse
    def batch_create_alerts(self, alerts: List[AlertDetailCreate]) -> BatchCreateResponse
    def stream_create_alert(self, alert_data: AlertDetailCreate) -> AlertDetailResponse

    # 数据查询
    def get_alert_by_id(self, alert_id: str) -> Optional[AlertDetailResponse]
    def get_alerts_by_device(self, device_id: str) -> List[AlertDetailResponse]
    def get_alerts_by_time_range(self, start_time: datetime, end_time: datetime) -> List[AlertDetailResponse]
    def search_alerts(self, filters: AlertSearchFilters) -> PaginatedResponse[AlertDetailResponse]

    # 状态更新
    def update_alert_status(self, alert_id: str, status: str) -> bool
    def update_alert_fields(self, alert_id: str, update_data: AlertDetailUpdate) -> AlertDetailResponse

    # 关联操作
    def link_alert_to_session(self, alert_id: str, session_id: str) -> bool
    def link_alert_to_entity(self, alert_id: str, entity_id: str, entity_type: str) -> bool

```

### 4.2.2 研判管理接口

```python
class VerdictService:
    """研判管理服务"""

    # 研判记录管理
    def create_verdict(self, verdict_data: VerdictCreate) -> VerdictResponse
    def get_verdicts_by_alert(self, alert_id: str) -> List[VerdictResponse]
    def get_latest_verdict(self, alert_id: str) -> Optional[VerdictResponse]
    def update_verdict(self, verdict_id: str, update_data: VerdictUpdate) -> VerdictResponse

    # 批量研判
    def batch_apply_verdict(self, alert_ids: List[str], verdict_data: VerdictCreate) -> BatchVerdictResponse
    def apply_verdict_to_similar_alerts(self, alert_id: str, verdict_data: VerdictCreate) -> BatchVerdictResponse

```

### 4.2.3 实体管理接口

```python
class EntityService:
    """风险实体管理服务"""

    # 实体创建和去重
    def create_entity(self, entity_data: EntityCreate) -> EntityResponse
    def find_or_create_entity(self, entity_data: EntityCreate) -> tuple[EntityResponse, bool]
    def batch_create_entities(self, entities: List[EntityCreate]) -> BatchCreateResponse

    # 实体查询
    def get_entity_by_id(self, entity_id: str) -> Optional[EntityResponse]
    def get_entities_by_type(self, entity_type: str) -> List[EntityResponse]
    def search_entities(self, search_params: EntitySearchParams) -> PaginatedResponse[EntityResponse]

    # 去重相关功能
    def find_existing_entity(self, match_criteria: dict, entity_type: str) -> Optional[EntityResponse]
    def get_match_criteria(self, entity_data: EntityCreate) -> dict
    def update_entity_on_reuse(self, entity_id: str, entity_data: EntityCreate) -> bool

    # 关联查询
    def get_related_alerts(self, entity_id: str) -> List[AlertDetailResponse]
    def get_entity_relationships(self, entity_id: str) -> List[RelationshipResponse]
    def find_entity_path(self, from_entity_id: str, to_entity_id: str) -> List[PathResponse]

    # 关系管理
    def create_evidence_entity_relationship(self, evidence_id: str, entity_id: str, 
                                          entity_type: str, is_new: bool, 
                                          entity_data: dict) -> bool

    # 统计分析
    def get_entity_reuse_stats(self) -> dict
    def get_deduplication_metrics(self) -> dict

```

### 4.3 REST API接口设计

### 4.3.1 告警相关API

```python
# 告警细节管理
POST   /api/v1/alerts                    # 创建告警细节
POST   /api/v1/alerts/batch              # 批量创建告警细节
GET    /api/v1/alerts/{alert_id}         # 获取告警细节
PUT    /api/v1/alerts/{alert_id}         # 更新告警细节
DELETE /api/v1/alerts/{alert_id}         # 删除告警细节
GET    /api/v1/alerts                    # 查询告警细节列表

# 告警状态管理
PUT    /api/v1/alerts/{alert_id}/status  # 更新告警状态
GET    /api/v1/alerts/status/{status}    # 按状态查询告警

# 告警关联管理
POST   /api/v1/alerts/{alert_id}/entities/{entity_id}  # 关联实体
DELETE /api/v1/alerts/{alert_id}/entities/{entity_id}  # 取消关联实体
GET    /api/v1/alerts/{alert_id}/entities              # 获取关联实体
```

### 4.3.2 研判相关API

```python
# 研判记录管理
POST   /api/v1/verdicts                          # 创建研判记录
GET    /api/v1/verdicts/{verdict_id}             # 获取研判记录
PUT    /api/v1/verdicts/{verdict_id}             # 更新研判记录
DELETE /api/v1/verdicts/{verdict_id}             # 删除研判记录

# 告警研判管理
POST   /api/v1/alerts/{alert_id}/verdicts        # 为告警添加研判
GET    /api/v1/alerts/{alert_id}/verdicts        # 获取告警研判记录
GET    /api/v1/alerts/{alert_id}/verdicts/latest # 获取最新研判
```

### 4.3.3 实体相关API

```python
# 实体管理
POST   /api/v1/entities                    # 创建实体
POST   /api/v1/entities/batch              # 批量创建实体
GET    /api/v1/entities/{entity_id}        # 获取实体
PUT    /api/v1/entities/{entity_id}        # 更新实体
DELETE /api/v1/entities/{entity_id}        # 删除实体
GET    /api/v1/entities                    # 查询实体列表

# 实体关联查询
GET    /api/v1/entities/{entity_id}/alerts      # 获取实体关联告警
GET    /api/v1/entities/{entity_id}/relations   # 获取实体关系
GET    /api/v1/entities/types/{entity_type}     # 按类型查询实体
```

## 5. 核心业务流程详细说明

### 5.1 告警细节节点创建流程

基于 `app/services/alert_service.py` 中的 `create_alert_detail` 方法实现：

```python
def create_alert_detail(self, alert_data: AlertDetailCreate) -> AlertDetailResponse:
    """
    告警细节创建完整流程

    1. 生成唯一标识
    2. 准备节点属性
    3. 创建告警节点
    4. 处理证据信息
    5. 提取设备信息
    6. 建立关联关系
    7. 返回响应
    """
```

#### 5.1.1 详细步骤说明

**步骤1: 生成唯一标识**
```python
# 生成图数据库节点ID
vid = str(uuid4())
current_time = datetime.now()
```

**步骤2: 准备节点属性**
```python
properties = {
    "vid": vid,
    "alert_detail_id": alert_data.alert_detail_id,
    "alert_name": alert_data.alert_name,
    "alert_message": alert_data.alert_message,
    "vulnerabilities": alert_data.vulnerabilities,
    "product_name": alert_data.product_name,
    "time": current_time.isoformat(),
    "confidence_score": alert_data.confidence_score,
    "impact": alert_data.impact.value,
    "risk": alert_data.risk.value,
    "severity": alert_data.severity.value,
    "status": alert_data.status.value,
    "comment": alert_data.comment,
    "raw_data": alert_data.raw_data,
    "created_at": current_time.isoformat(),
    "updated_at": current_time.isoformat()
}
```

**步骤3: 创建告警节点**
```python
# 使用BaseRepository创建AlertDetail节点
node_id = self.repository.create_node("AlertDetail", properties)
```

**步骤4: 处理证据信息**
```python
# 如果存在evidence_artifacts，创建Evidence节点并建立关系
if alert_data.evidence_artifacts:
    evidence_id = self.evidence_service.process_evidence_artifacts(
        vid, alert_data.evidence_artifacts
    )
```

**步骤5: 提取设备信息**
```python
# 从告警数据中提取设备信息，创建或更新Device节点
device_id = self.device_service.extract_and_create_device(alert_data)
if device_id:
    self.device_service.link_alert_to_device(vid, device_id)
```

### 5.2 证据节点生成逻辑和关联机制

基于 `app/services/evidence_service.py` 中的实际实现：

#### 5.2.1 证据节点创建流程

```python
def process_evidence_artifacts(self, alert_id: str, evidence_artifacts: List[EvidenceArtifactsModel]) -> str:
    """
    证据处理完整流程

    1. 创建单个Evidence节点（合并所有evidence_artifacts）
    2. 从所有evidence_artifacts中提取风险实体
    3. 创建或关联风险实体
    4. 建立Evidence->Entity关系
    5. 建立AlertDetail->Evidence关系
    """
```

**步骤1: 创建Evidence节点**
```python
# 生成基于内容的确定性ID
evidence_id = self._generate_evidence_id("combined_evidence", all_evidence_content)

# 合并所有evidence_artifacts信息
properties = {
    "evidence_id": evidence_id,
    "evidence_type": "combined_evidence",
    "evidence_types": evidence_types,  # 所有证据类型列表
    "is_alert_trigger": has_trigger,   # 是否包含触发证据
    "include_payload": has_payload,    # 是否包含攻击载荷
    "evidence_count": len(evidence_artifacts),
    "raw_evidence_data": json.dumps(all_raw_data, ensure_ascii=False)
}

# 创建Evidence节点
node_id = self.repository.create_node("Evidence", properties)
```

**步骤2: 建立AlertDetail->Evidence关系**
```python
# 创建HAS_EVIDENCE关系
relationship_created = self.repository.create_relationship(
    alert_id,
    evidence_id,
    RelationshipTypes.HAS_EVIDENCE,
    {
        "created_time": current_time.isoformat(),
        "is_trigger": has_trigger,
        "has_payload": has_payload,
        "evidence_count": len(evidence_artifacts)
    },
    "AlertDetail",  # from_label
    "Evidence"      # to_label
)
```

#### 5.2.2 风险实体提取和关联流程

**支持的实体类型**:
- HttpRequestEntity (HTTP请求实体)
- HttpResponseEntity (HTTP响应实体)
- DnsQueryEntity (DNS查询实体)
- NetworkEndpointEntity (网络端点实体)
- UrlEntity (URL实体)
- FileEntity (文件实体)
- ProcessEntity (进程实体)

**提取逻辑**:
```python
def _extract_risk_entities(self, evidence: EvidenceArtifactsModel) -> List[Dict[str, Any]]:
    """从单个evidence_artifact中提取所有风险实体"""
    entities = []

    # 根据evidence内容提取不同类型的实体
    if evidence.http_request:
        entities.append(self._extract_http_request_entity(evidence.http_request))
        if evidence.http_request.url:
            entities.append(self._extract_url_entity(evidence.http_request.url))

    if evidence.src_endpoint:
        entities.append(self._extract_network_endpoint_entity(evidence.src_endpoint, "src"))

    # ... 其他实体类型提取

    return entities
```

**实体关联逻辑**:
```python
def _link_evidence_to_entity(self, evidence_id: str, entity_id: str, entity_type: EntityType, entity_data: dict, is_new: bool):
    """创建Evidence->Entity的RELATES_TO关系"""
    rel_properties = {
        "entity_type": entity_type.value,
        "created_time": current_time.isoformat(),
        "extraction_reason": "从证据中自动提取",
        "is_new_entity": is_new,
        "entity_context": json.dumps({
            "entity_name": entity_data.get("name", ""),
            "risk_score": entity_data.get("risk_score", 0),
            "properties_snapshot": entity_data.get("properties", {}),
            "extraction_time": current_time.isoformat()
        }, ensure_ascii=False)
    }

    # 创建RELATES_TO关系
    self.repository.create_relationship(
        evidence_id, entity_id, RelationshipTypes.RELATES_TO,
        rel_properties, "Evidence", entity_label
    )
```

### 5.3 风险实体去重、创建的完整流程

基于 `app/services/entity_service.py` 中的 `find_or_create_entity` 方法：

#### 5.3.1 实体去重流程

```python
def find_or_create_entity(self, entity_data: EntityCreate) -> tuple[EntityResponse, bool]:
    """
    实体去重核心流程

    1. 查找现有匹配实体
    2. 如果找到：更新复用信息，返回现有实体
    3. 如果未找到：创建新实体
    4. 返回(实体响应, 是否新创建)
    """

    # 先尝试查找现有实体
    existing_entity = self._find_existing_entity(entity_data)

    if existing_entity:
        # 找到现有实体，更新时间和风险分数
        self._update_entity_on_reuse(existing_entity.entity_id, entity_data)
        return existing_entity, False
    else:
        # 创建新实体
        new_entity = self.create_entity(entity_data)
        return new_entity, True
```

**当前实现状态**: 框架已实现，`_find_existing_entity` 方法需要完善具体匹配逻辑

#### 5.3.2 实体创建流程

```python
def create_entity(self, entity_data: EntityCreate) -> EntityResponse:
    """创建新实体节点"""

    # 生成唯一ID
    entity_id = str(uuid4())
    current_time = datetime.now()

    # 准备节点属性
    properties = {
        "entity_id": entity_id,
        "name": entity_data.name,
        "risk_score": entity_data.risk_score,
        "description": entity_data.description,
        "reuse_count": 1,  # 初始复用次数为1
        "created_at": current_time.isoformat(),
        "updated_at": current_time.isoformat()
    }

    # 添加实体特定属性
    properties.update(entity_data.properties)

    # 为ProcessEntity添加特殊字段
    if entity_data.entity_type.value == "ProcessEntity":
        if "process_name" in entity_data.properties and "command_line" in entity_data.properties:
            process_signature = f"{entity_data.properties['process_name']}|{entity_data.properties['command_line']}"
            properties["process_signature"] = hashlib.md5(process_signature.encode()).hexdigest()

    # 创建节点，使用实体类型作为标签
    self.repository.create_node(entity_data.entity_type.value, properties)

    return EntityResponse(...)
```

### 5.4 设备信息提取和关联逻辑

基于 `app/services/device_service.py` 中的实现：

#### 5.4.1 设备信息提取

```python
def extract_and_create_device(self, alert_data: AlertDetailCreate) -> Optional[str]:
    """
    从告警数据中提取设备信息

    1. 从告警数据中提取设备相关字段
    2. 生成设备ID
    3. 检查设备是否已存在
    4. 如果存在：更新告警计数
    5. 如果不存在：创建新设备节点
    """

    device_info = self._extract_device_info(alert_data)
    if not device_info:
        return None

    device_id = self._generate_device_id(device_info)
    existing_device = self._get_device_by_id(device_id)

    if existing_device:
        self._increment_alert_count(device_id)
        return device_id
    else:
        return self._create_device_node(device_id, device_info)
```

#### 5.4.2 设备关联

```python
def link_alert_to_device(self, alert_id: str, device_id: str):
    """创建AlertDetail->Device的GENERATED_BY关系"""

    relationship_created = self.repository.create_relationship(
        alert_id, device_id, RelationshipTypes.GENERATED_BY,
        {
            "created_time": current_time.isoformat(),
            "relationship_reason": "告警由此设备生成"
        },
        "AlertDetail", "Device"
    )
```

## 6. 风险实体去重机制设计

### 6.1 实现状态

**已完整实现**:
- 实体去重框架 (`find_or_create_entity` 方法)
- 智能匹配算法 (`_find_existing_entity` 方法)
- 6种实体类型的完整匹配策略
- 实体创建和更新逻辑
- 特殊字段处理（process_signature、body_hash）

### 6.1.1 核心设计原则

- **共性优先**: 风险实体存储多个告警的共性特征，通过匹配算法识别相同实体
- **分层匹配**: 主要字段优先，辅助字段补充，备选字段兜底
- **跨设备兼容**: 考虑不同安全设备（WAF、IDS、EDR等）的数据格式差异
- **真实场景导向**: 匹配逻辑符合实际安全运营中的实体识别需求
- **容错处理**: 关键字段缺失时使用备选方案，处理数据质量问题

### 6.2 智能匹配算法实现

#### 6.2.1 匹配流程概述

基于 `app/services/entity_service.py` 中的完整实现：

```python
def find_or_create_entity(self, entity_data: EntityCreate) -> tuple[EntityResponse, bool]:
    """
    实体去重核心流程

    1. 生成智能匹配条件
    2. 查找现有匹配实体
    3. 如果找到：更新复用信息，返回现有实体
    4. 如果未找到：创建新实体
    5. 返回(实体响应, 是否新创建)
    """

    # 先尝试查找现有实体
    existing_entity = self._find_existing_entity(entity_data)

    if existing_entity:
        # 找到现有实体，更新时间和风险分数
        self._update_entity_on_reuse(existing_entity.entity_id, entity_data)
        return existing_entity, False
    else:
        # 创建新实体
        new_entity = self.create_entity(entity_data)
        return new_entity, True
```

#### 6.2.2 智能查询构建

```python
def _find_existing_entity(self, entity_data: EntityCreate) -> Optional[EntityResponse]:
    """
    智能实体查找算法

    实现特性：
    1. 分层匹配条件生成
    2. 特殊字段处理（如HTTP响应的body_hash）
    3. 数据类型容错处理
    4. 查询性能优化
    """

    entity_type = entity_data.entity_type.value
    match_criteria = self._get_match_criteria(entity_data)

    if not match_criteria:
        return None

    # 特殊处理：HTTP响应实体的body_hash优化
    if entity_type == "HttpResponseEntity" and "body" in match_criteria:
        body_hash = hashlib.md5(match_criteria["body"].encode()).hexdigest()
        existing_with_hash = self._find_entity_with_body_hash(entity_type, match_criteria, body_hash)
        if existing_with_hash:
            return existing_with_hash

    # 构建查询条件，处理不同数据类型
    where_clauses = []
    params = {}

    for key, value in match_criteria.items():
        if value is None:
            where_clauses.append(f"e.{key} IS NULL")
        elif isinstance(value, (int, float)):
            where_clauses.append(f"e.{key} = ${key}")
            params[key] = value
        elif isinstance(value, str):
            if value.strip():
                where_clauses.append(f"e.{key} = ${key}")
                params[key] = value
            else:
                where_clauses.append(f"(e.{key} = ${key} OR e.{key} IS NULL)")
                params[key] = value

    # 执行查询，按更新时间排序获取最新实体
    query = f"""
    MATCH (e:{entity_type})
    WHERE {' AND '.join(where_clauses)}
    RETURN e, labels(e) as entity_labels
    ORDER BY e.updated_at DESC, e.created_at DESC
    LIMIT 1
    """

    result = self.connection.execute_read_transaction(query, params)

    if result:
        node_data = result[0]["e"]
        return self._convert_to_response(node_data, entity_type)

    return None
```

### 6.3 实体类型匹配策略详解

#### 6.3.1 网络端点实体 (NetworkEndpointEntity)

**匹配条件优先级**:
1. **主要匹配**: `ip_address` + `port` (精确匹配)
2. **辅助匹配**: `ip_address` + `endpoint_type` (当端口缺失时)
3. **备选匹配**: 仅 `ip_address` (当端口和类型都缺失时)

**跨设备兼容性**:
- **WAF设备**: 通常有完整的IP+端口信息
- **IDS设备**: 可能只有IP信息
- **EDR设备**: 可能有endpoint_type区分

**实现代码**:
```python
def _get_network_endpoint_criteria(self, properties: dict) -> dict:
    """网络端点实体匹配策略"""
    criteria = {}

    # 必须有IP地址才能匹配
    if not properties.get("ip_address"):
        return {}

    criteria["ip_address"] = properties["ip_address"]

    # 优先使用IP+端口进行精确匹配
    if properties.get("port") is not None:
        criteria["port"] = properties["port"]
    # 如果没有端口但有endpoint_type，使用IP+类型匹配
    elif properties.get("endpoint_type"):
        criteria["endpoint_type"] = properties["endpoint_type"]

    return criteria
```

**匹配示例**:
```python
# 精确匹配示例
properties = {
    "ip_address": "*************",
    "port": 8080,
    "endpoint_type": "src"
}
# 匹配条件: ip_address=************* AND port=8080

# 辅助匹配示例
properties = {
    "ip_address": "*************",
    "endpoint_type": "dst"
}
# 匹配条件: ip_address=************* AND endpoint_type=dst

# 备选匹配示例
properties = {
    "ip_address": "********"
}
# 匹配条件: ip_address=********
```

#### 6.3.2 文件实体 (FileEntity)

**匹配条件优先级**:
1. **主要匹配**: `file_hash` (任何哈希算法，最可靠)
2. **辅助匹配**: `filename` + `file_size` (当哈希缺失时)
3. **备选匹配**: 仅 `filename` (当文件名也缺失时)
4. **最后备选**: `file_path` (当文件名也缺失时)

**跨设备兼容性**:
- **EDR设备**: 通常提供完整哈希信息
- **沙箱设备**: 可能只有文件名和大小
- **网络设备**: 可能只有文件路径信息

**实现代码**:
```python
def _get_file_entity_criteria(self, properties: dict) -> dict:
    """文件实体匹配策略"""

    # 优先级1：任何类型的文件哈希
    hash_fields = ["file_hash", "hash_md5", "hash_sha1", "hash_sha256"]
    for hash_field in hash_fields:
        if properties.get(hash_field):
            return {hash_field: properties[hash_field]}

    # 优先级2：文件名+文件大小组合匹配
    if properties.get("filename") and properties.get("file_size"):
        return {
            "filename": properties["filename"],
            "file_size": properties["file_size"]
        }

    # 优先级3：仅文件名匹配（风险较高，但必要时使用）
    if properties.get("filename"):
        return {"filename": properties["filename"]}

    # 优先级4：文件路径匹配（最后备选）
    if properties.get("file_path"):
        return {"file_path": properties["file_path"]}

    return {}
```

**匹配示例**:
```python
# 主要匹配示例（文件哈希）
properties = {
    "file_hash": "d41d8cd98f00b204e9800998ecf8427e",
    "filename": "malware.exe",
    "file_size": 1024
}
# 匹配条件: file_hash=d41d8cd98f00b204e9800998ecf8427e

# 辅助匹配示例（文件名+大小）
properties = {
    "filename": "suspicious.bat",
    "file_size": 512,
    "file_path": "/tmp/suspicious.bat"
}
# 匹配条件: filename=suspicious.bat AND file_size=512

# 备选匹配示例（仅文件名）
properties = {
    "filename": "unknown.exe"
}
# 匹配条件: filename=unknown.exe
```

**设计理由**:
- **文件哈希**是最可靠的匹配方式，相同哈希必然是同一文件
- **文件名+大小**组合提高匹配准确性，避免同名不同文件的误匹配
- **文件路径**不作为主要匹配字段，因为同一文件可能被复制到不同位置
- 支持多种哈希算法，兼容不同设备的数据格式

#### 6.3.3 进程实体 (ProcessEntity)

**匹配条件优先级**:
1. **主要匹配**: `process_signature` (进程名+命令行的MD5哈希)
2. **辅助匹配**: `process_name` + `process_path` (进程名+路径组合)
3. **备选匹配**: 仅 `process_name` (仅进程名)

**跨设备兼容性**:
- **EDR设备**: 提供完整的进程信息
- **系统日志**: 可能只有进程名
- **网络设备**: 可能从User-Agent等推断进程

**实现代码**:
```python
def _get_process_entity_criteria(self, properties: dict) -> dict:
    """进程实体匹配策略"""

    # 优先级1：进程签名匹配（最精确）
    if properties.get("process_name") and properties.get("command_line"):
        process_signature = f"{properties['process_name']}|{properties['command_line']}"
        process_hash = hashlib.md5(process_signature.encode()).hexdigest()
        return {"process_signature": process_hash}

    # 优先级2：进程名+路径匹配
    if properties.get("process_name") and properties.get("process_path"):
        return {
            "process_name": properties["process_name"],
            "process_path": properties["process_path"]
        }

    # 优先级3：仅进程名匹配
    if properties.get("process_name"):
        return {"process_name": properties["process_name"]}

    return {}
```

**匹配示例**:
```python
# 主要匹配示例（进程签名）
properties = {
    "process_name": "cmd.exe",
    "command_line": "/c whoami",
    "process_path": "C:\\Windows\\System32\\cmd.exe"
}
# 匹配条件: process_signature=<MD5哈希值>

# 辅助匹配示例（进程名+路径）
properties = {
    "process_name": "svchost.exe",
    "process_path": "C:\\Windows\\System32\\svchost.exe"
}
# 匹配条件: process_name=svchost.exe AND process_path=C:\Windows\System32\svchost.exe

# 备选匹配示例（仅进程名）
properties = {
    "process_name": "unknown.exe"
}
# 匹配条件: process_name=unknown.exe
```

**特殊处理**:
- 系统自动生成 `process_signature` 字段用于精确匹配
- 在创建和匹配时都计算相同的MD5哈希值
- 进程签名格式：`进程名|命令行`，使用管道符分隔

#### 6.3.4 HTTP请求实体 (HttpRequestEntity)

**匹配条件优先级**:
1. **主要匹配**: `method` + `url` + `user_agent` (完整请求特征)
2. **辅助匹配**: `method` + `url` (当User-Agent缺失时)
3. **备选匹配**: 仅 `url` (当方法缺失时)

**跨设备兼容性**:
- **WAF设备**: 提供完整的HTTP请求信息
- **代理设备**: 可能缺少某些头部信息
- **IDS设备**: 可能只有URL信息

**实现代码**:
```python
def _get_http_request_criteria(self, properties: dict) -> dict:
    """HTTP请求实体匹配策略"""

    # 优先级1：方法+URL+User-Agent三元组匹配（最精确）
    if (properties.get("method") and
        properties.get("url") and
        properties.get("user_agent")):
        return {
            "method": properties["method"],
            "url": properties["url"],
            "user_agent": properties["user_agent"]
        }

    # 优先级2：方法+URL匹配
    if properties.get("method") and properties.get("url"):
        return {
            "method": properties["method"],
            "url": properties["url"]
        }

    # 优先级3：仅URL匹配（风险较高，但对于GET请求可能合理）
    if properties.get("url"):
        return {"url": properties["url"]}

    return {}
```

**匹配示例**:
```python
# 主要匹配示例（完整三元组）
properties = {
    "method": "POST",
    "url": "/admin/login",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
}
# 匹配条件: method=POST AND url=/admin/login AND user_agent=Mozilla/5.0...

# 辅助匹配示例（方法+URL）
properties = {
    "method": "GET",
    "url": "/shell.php"
}
# 匹配条件: method=GET AND url=/shell.php

# 备选匹配示例（仅URL）
properties = {
    "url": "/index.html"
}
# 匹配条件: url=/index.html
```

#### 6.3.5 URL实体 (UrlEntity)

**匹配条件优先级**:
1. **主要匹配**: `full_url` (完整URL，最精确)
2. **辅助匹配**: `url` (URL字段匹配，兼容性考虑)
3. **备选匹配**: `hostname` + `path` (当完整URL缺失时)
4. **最后备选**: 仅 `hostname` (仅主机名匹配)

**跨设备兼容性**:
- **网络设备**: 通常提供完整URL
- **DNS设备**: 可能只有hostname
- **代理设备**: 可能分别记录各个组件

**实现代码**:
```python
def _get_url_entity_criteria(self, properties: dict) -> dict:
    """URL实体匹配策略"""

    # 优先级1：完整URL匹配
    if properties.get("full_url"):
        return {"full_url": properties["full_url"]}

    # 优先级2：URL字段匹配（兼容性考虑）
    if properties.get("url"):
        return {"url": properties["url"]}

    # 优先级3：hostname+path组合匹配
    if properties.get("hostname") and properties.get("path"):
        return {
            "hostname": properties["hostname"],
            "path": properties["path"]
        }

    # 优先级4：仅hostname匹配
    if properties.get("hostname"):
        return {"hostname": properties["hostname"]}

    return {}
```

#### 6.3.6 DNS查询实体 (DnsQueryEntity)

**匹配条件优先级**:
1. **主要匹配**: `query_name` + `query_type` (完整查询特征)
2. **辅助匹配**: 仅 `query_name` (当查询类型缺失时)
3. **备选匹配**: `hostname` (兼容性字段)

**跨设备兼容性**:
- **DNS服务器**: 提供完整查询信息
- **网络监控**: 可能只有域名信息
- **代理设备**: 可能使用不同字段名

**实现代码**:
```python
def _get_dns_query_criteria(self, properties: dict) -> dict:
    """DNS查询实体匹配策略"""

    # 优先级1：查询名+查询类型匹配
    if properties.get("query_name") and properties.get("query_type"):
        return {
            "query_name": properties["query_name"],
            "query_type": properties["query_type"]
        }

    # 优先级2：仅查询名匹配
    if properties.get("query_name"):
        return {"query_name": properties["query_name"]}

    # 优先级3：hostname字段匹配（兼容性）
    if properties.get("hostname"):
        return {"hostname": properties["hostname"]}

    return {}
```

**匹配示例**:
```python
# 主要匹配示例（查询名+类型）
properties = {
    "query_name": "malware.example.com",
    "query_type": "A",
    "hostname": "malware.example.com"
}
# 匹配条件: query_name=malware.example.com AND query_type=A

# 辅助匹配示例（仅查询名）
properties = {
    "query_name": "suspicious.net"
}
# 匹配条件: query_name=suspicious.net

# 备选匹配示例（hostname兼容性）
properties = {
    "hostname": "legacy.domain.com"
}
# 匹配条件: hostname=legacy.domain.com
```

#### 6.3.7 HTTP响应实体 (HttpResponseEntity)

**匹配条件优先级**:
1. **主要匹配**: `status_code` + `content_type` + `body_hash` (最精确)
2. **辅助匹配**: `status_code` + `content_type` (中等精确度)
3. **备选匹配**: 仅 `status_code` (仅状态码，风险高)

**特殊处理**:
- 自动为响应体生成MD5哈希用于匹配
- 创建时添加body_hash字段
- 查询时优先使用哈希匹配

**实现代码**:
```python
def _get_http_response_criteria(self, properties: dict) -> dict:
    """HTTP响应实体匹配策略"""

    # 优先级1：状态码+内容类型+响应体哈希（如果有）
    if (properties.get("status_code") and
        properties.get("content_type") and
        properties.get("body")):
        # 为响应体生成哈希用于匹配
        body_hash = hashlib.md5(properties["body"].encode()).hexdigest()
        return {
            "status_code": properties["status_code"],
            "content_type": properties["content_type"],
            "body_hash": body_hash
        }

    # 优先级2：状态码+内容类型匹配
    if properties.get("status_code") and properties.get("content_type"):
        return {
            "status_code": properties["status_code"],
            "content_type": properties["content_type"]
        }

    # 优先级3：仅状态码匹配（风险很高，仅在必要时使用）
    if properties.get("status_code"):
        return {"status_code": properties["status_code"]}

    return {}
```

**匹配示例**:
```python
# 主要匹配示例（状态码+内容类型+body哈希）
properties = {
    "status_code": 200,
    "content_type": "text/html",
    "body": "<html><body>Hello World</body></html>"
}
# 匹配条件: status_code=200 AND content_type=text/html AND body_hash=<MD5哈希>

# 辅助匹配示例（状态码+内容类型）
properties = {
    "status_code": 404,
    "content_type": "text/html"
}
# 匹配条件: status_code=404 AND content_type=text/html
```

**注意事项**:
- HTTP响应的匹配相对复杂，因为相同的响应可能出现在不同的请求中
- body_hash字段在创建实体时自动生成，用于提高匹配精度
- 仅状态码匹配风险很高，应谨慎使用

### 6.4 容错处理和数据质量保证

#### 6.4.1 数据类型容错处理

智能匹配算法实现了完善的数据类型容错处理：

```python
# 在 _find_existing_entity 方法中的容错处理
for key, value in match_criteria.items():
    if value is None:
        # 处理None值
        where_clauses.append(f"e.{key} IS NULL")
    elif isinstance(value, (int, float)):
        # 数值类型直接匹配
        where_clauses.append(f"e.{key} = ${key}")
        params[key] = value
    elif isinstance(value, str):
        # 字符串类型，处理空字符串
        if value.strip():
            where_clauses.append(f"e.{key} = ${key}")
            params[key] = value
        else:
            where_clauses.append(f"(e.{key} = ${key} OR e.{key} IS NULL)")
            params[key] = value
    else:
        # 其他类型转换为字符串
        where_clauses.append(f"e.{key} = ${key}")
        params[key] = str(value)
```

#### 6.4.2 特殊字段处理

**进程签名自动生成**:
```python
# 在创建ProcessEntity时自动生成process_signature
if entity_data.entity_type.value == "ProcessEntity":
    if ("process_name" in entity_data.properties and
        "command_line" in entity_data.properties):
        process_signature = f"{entity_data.properties['process_name']}|{entity_data.properties['command_line']}"
        properties["process_signature"] = hashlib.md5(process_signature.encode()).hexdigest()
```

**HTTP响应body_hash自动生成**:
```python
# 在创建HttpResponseEntity时自动生成body_hash
elif entity_data.entity_type.value == "HttpResponseEntity":
    if "body" in entity_data.properties and entity_data.properties["body"]:
        body_hash = hashlib.md5(entity_data.properties["body"].encode()).hexdigest()
        properties["body_hash"] = body_hash
```

#### 6.4.3 查询优化机制

**时间排序优化**:
```python
# 获取最新的匹配实体，避免返回过时的实体
query = f"""
MATCH (e:{entity_type})
WHERE {where_clause}
RETURN e, labels(e) as entity_labels
ORDER BY e.updated_at DESC, e.created_at DESC
LIMIT 1
"""
```

**特殊查询优化**:
```python
# 为HTTP响应实体提供body_hash优化查询
def _find_entity_with_body_hash(self, entity_type: str, match_criteria: dict, body_hash: str):
    """专门的body_hash查询优化"""
    hash_criteria = {k: v for k, v in match_criteria.items() if k != "body"}
    hash_criteria["body_hash"] = body_hash

    # 执行优化的哈希查询
    # ... 查询逻辑
```

### 6.5 实体复用更新逻辑

#### 6.5.1 复用时的字段更新

当找到匹配的现有实体时，系统会智能更新相关字段

#### 6.5.2 复用统计信息

每个实体维护以下复用相关信息：

- **reuse_count**: 实体被复用的次数（初始为1）
- **created_at**: 实体首次创建时间
- **updated_at**: 实体最后更新时间
- **risk_score**: 历史最高风险分数

这些信息有助于：
1. 识别高频出现的风险实体
2. 分析实体的时间分布特征
3. 评估实体的威胁等级变化

### 6.6 性能优化策略

#### 6.6.1 数据库索引设计

为提高匹配效率，在关键匹配字段上建立复合索引：

```cypher
-- 网络端点实体索引（支持分层匹配）
CREATE INDEX idx_network_endpoint_ip_port IF NOT EXISTS
FOR (n:NetworkEndpointEntity) ON (n.ip_address, n.port);

CREATE INDEX idx_network_endpoint_ip_type IF NOT EXISTS
FOR (n:NetworkEndpointEntity) ON (n.ip_address, n.endpoint_type);

-- 文件实体索引（支持多种哈希算法）
CREATE INDEX idx_file_hash IF NOT EXISTS
FOR (n:FileEntity) ON (n.file_hash);

CREATE INDEX idx_file_hash_md5 IF NOT EXISTS
FOR (n:FileEntity) ON (n.hash_md5);

CREATE INDEX idx_file_hash_sha256 IF NOT EXISTS
FOR (n:FileEntity) ON (n.hash_sha256);

CREATE INDEX idx_file_name_size IF NOT EXISTS
FOR (n:FileEntity) ON (n.filename, n.file_size);

-- 进程实体索引
CREATE INDEX idx_process_signature IF NOT EXISTS
FOR (n:ProcessEntity) ON (n.process_signature);

CREATE INDEX idx_process_name_path IF NOT EXISTS
FOR (n:ProcessEntity) ON (n.process_name, n.process_path);

-- HTTP请求实体索引（支持三元组匹配）
CREATE INDEX idx_http_request_method_url_ua IF NOT EXISTS
FOR (n:HttpRequestEntity) ON (n.method, n.url, n.user_agent);

CREATE INDEX idx_http_request_method_url IF NOT EXISTS
FOR (n:HttpRequestEntity) ON (n.method, n.url);

-- URL实体索引
CREATE INDEX idx_url_full IF NOT EXISTS
FOR (n:UrlEntity) ON (n.full_url);

CREATE INDEX idx_url_hostname_path IF NOT EXISTS
FOR (n:UrlEntity) ON (n.hostname, n.path);

-- DNS查询实体索引
CREATE INDEX idx_dns_query_name_type IF NOT EXISTS
FOR (n:DnsQueryEntity) ON (n.query_name, n.query_type);

-- HTTP响应实体索引
CREATE INDEX idx_http_response_status_content_hash IF NOT EXISTS
FOR (n:HttpResponseEntity) ON (n.status_code, n.content_type, n.body_hash);

-- 通用时间索引（用于排序优化）
CREATE INDEX idx_entity_updated_at IF NOT EXISTS
FOR (n) ON (n.updated_at);
```


### 6.7 匹配算法总结

#### 6.7.1 实现完整性

**已完整实现**:
- ✅ 6种实体类型的智能匹配策略
- ✅ 分层匹配条件生成
- ✅ 数据类型容错处理
- ✅ 特殊字段自动处理
- ✅ 实体复用更新逻辑
- ✅ 查询性能优化

#### 6.7.2 核心价值

**智能去重**:
- 避免重复实体创建，提高图谱质量
- 支持跨设备、跨时间的实体识别
- 减少存储空间和查询复杂度

**跨设备兼容**:
- 适配不同安全设备的数据格式
- 处理字段缺失和数据质量问题
- 支持渐进式匹配策略

**性能优化**:
- 索引友好的查询设计
- 分层匹配减少查询复杂度
- 特殊优化提升关键场景性能

#### 6.7.3 应用效果

通过智能实体匹配机制，AlertGraph系统能够：

1. **提高分析准确性**: 避免同一实体的多个副本影响关联分析
2. **优化存储效率**: 减少重复数据，提高存储利用率
3. **加速查询性能**: 通过实体复用减少图谱复杂度
4. **增强运营效率**: 减少安全分析师的重复工作

这套匹配机制是AlertGraph系统的核心竞争力，为后续的图谱分析和威胁检测提供了高质量的数据基础。

## 7. 当前实现状态总结

### 7.1 已完成功能

#### 7.1.1 核心节点类型 ✅
- **AlertDetail**: 完整实现，包含所有必要属性和验证
- **Verdict**: 完整实现，支持AI和人工研判
- **Evidence**: 完整实现，支持多种证据类型合并
- **Device**: 完整实现，支持设备信息提取和去重

#### 7.1.2 关系类型 ✅
- **HAS_EVIDENCE**: AlertDetail -> Evidence，包含触发和载荷信息
- **RELATES_TO**: Evidence -> Entity，包含实体上下文快照
- **GENERATED_BY**: AlertDetail -> Device，包含生成原因
- **HAS_VERDICT**: AlertDetail -> Verdict，支持多次研判

#### 7.1.3 业务流程 ✅
- **告警创建**: 完整的告警细节创建流程
- **证据处理**: 自动提取和关联风险实体
- **设备关联**: 自动提取设备信息并建立关联
- **研判管理**: 支持告警研判记录的创建和查询

#### 7.1.4 实体管理 ✅
- **实体创建**: 支持7种实体类型的创建
- **智能去重**: 完整的实体去重机制已实现
- **匹配算法**: 6种实体类型的智能匹配策略已完成
- **关系管理**: 实体间关系的创建和查询
- **复用逻辑**: 实体复用时的更新机制

#### 7.1.5 智能匹配机制 ✅
- **分层匹配**: 主要、辅助、备选匹配条件的完整实现
- **跨设备兼容**: 支持不同安全设备的数据格式差异
- **容错处理**: 完善的数据类型和空值处理
- **特殊优化**: HTTP响应body_hash、进程签名等特殊字段处理
- **性能优化**: 索引友好的查询设计和排序优化
