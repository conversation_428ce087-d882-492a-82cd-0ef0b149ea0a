#!/usr/bin/env python3
"""
优化版数据库清空脚本

针对连接慢的问题进行优化：
1. 减少连接创建次数
2. 简化统计查询
3. 优化删除策略
4. 添加连接复用
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from neo4j import GraphDatabase
from app.core.config import settings


class OptimizedNeo4jConnection:
    """优化的Neo4j连接类，减少连接开销"""
    
    def __init__(self):
        self.uri = settings.neo4j_uri
        self.user = settings.neo4j_user
        self.password = settings.neo4j_password
        self.database = settings.neo4j_database
        self._driver = None
    
    def connect(self):
        """创建连接但不验证（延迟验证）"""
        if not self._driver:
            print("⚡ 创建驱动...")
            start_time = time.time()
            self._driver = GraphDatabase.driver(
                self.uri,
                auth=(self.user, self.password),
                connection_timeout=5.0,  # 5秒连接超时
                max_connection_lifetime=3600,  # 1小时
            )
            print(f"   完成: {time.time() - start_time:.3f}秒")
        return self._driver
    
    def execute_query(self, query, parameters=None):
        """执行查询"""
        driver = self.connect()
        with driver.session(database=self.database) as session:
            result = session.run(query, parameters or {})
            return list(result)
    
    def close(self):
        """关闭连接"""
        if self._driver:
            self._driver.close()
            self._driver = None


def get_simple_stats(connection):
    """获取简化的统计信息"""
    try:
        print("📊 获取数据库统计...")
        start_time = time.time()
        
        # 只查询必要的统计
        result = connection.execute_query("MATCH (n) RETURN count(n) as node_count")
        node_count = result[0]['node_count'] if result else 0
        
        query_time = time.time() - start_time
        print(f"   节点总数: {node_count} (查询耗时: {query_time:.3f}秒)")
        return node_count
        
    except Exception as e:
        print(f"⚠️ 获取统计失败: {str(e)}")
        return 0


def optimized_clear_all(connection, force=False):
    """优化的清空操作"""
    try:
        print("\n🗑️ 开始优化清空...")
        
        # 获取节点数量
        node_count = get_simple_stats(connection)
        
        if node_count == 0:
            print("✅ 数据库已经是空的")
            return True
        
        # 确认操作
        if not force:
            response = input(f"⚠️ 确认清空 {node_count} 个节点？(y/N): ")
            if response.lower() not in ['y', 'yes']:
                print("❌ 操作已取消")
                return False
        
        print(f"🔄 开始删除 {node_count} 个节点...")
        start_time = time.time()
        
        # 使用简单高效的删除策略
        if node_count < 10000:
            # 小量数据：直接删除
            print("   使用直接删除策略...")
            connection.execute_query("MATCH (n) DETACH DELETE n")
        else:
            # 大量数据：分批删除
            print("   使用分批删除策略...")
            batch_size = 5000
            deleted_total = 0
            
            while True:
                result = connection.execute_query(f"""
                    MATCH (n)
                    WITH n LIMIT {batch_size}
                    DETACH DELETE n
                    RETURN count(n) as deleted
                """)
                
                deleted = result[0]['deleted'] if result else 0
                if deleted == 0:
                    break
                
                deleted_total += deleted
                print(f"   已删除: {deleted_total}/{node_count}")
        
        total_time = time.time() - start_time
        print(f"✅ 删除完成，耗时: {total_time:.3f}秒")
        
        # 验证删除结果
        final_count = get_simple_stats(connection)
        print(f"📊 剩余节点: {final_count}")
        
        return final_count == 0
        
    except Exception as e:
        print(f"❌ 清空失败: {str(e)}")
        return False


def optimized_clear_alerts(connection, force=False):
    """优化的告警清空"""
    try:
        print("\n🗑️ 开始清空告警...")
        
        # 查询告警数量
        print("📊 查询告警数量...")
        result = connection.execute_query("MATCH (a:AlertDetail) RETURN count(a) as count")
        alert_count = result[0]['count'] if result else 0
        
        if alert_count == 0:
            print("✅ 没有告警数据")
            return True
        
        print(f"📋 发现 {alert_count} 个告警")
        
        # 确认操作
        if not force:
            response = input(f"⚠️ 确认清空 {alert_count} 个告警？(y/N): ")
            if response.lower() not in ['y', 'yes']:
                print("❌ 操作已取消")
                return False
        
        print("🔄 删除告警...")
        start_time = time.time()
        
        connection.execute_query("MATCH (a:AlertDetail) DETACH DELETE a")
        
        delete_time = time.time() - start_time
        print(f"✅ 告警删除完成，耗时: {delete_time:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 告警清空失败: {str(e)}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AlertGraph 优化版数据库清空工具")
    parser.add_argument("--mode", choices=['all', 'alerts'], default='all',
                       help="清空模式: all=清空所有数据, alerts=只清空告警数据")
    parser.add_argument("--force", action='store_true',
                       help="跳过确认直接执行")
    
    args = parser.parse_args()
    
    print("🚀 AlertGraph 优化版数据库清空工具")
    print("="*50)
    print(f"🔧 清空模式: {'所有数据' if args.mode == 'all' else '仅告警数据'}")
    
    # 记录总时间
    total_start_time = time.time()
    
    try:
        # 创建优化连接
        print("\n🔗 初始化连接...")
        connection = OptimizedNeo4jConnection()
        
        # 简单连接测试
        # print("🔍 测试连接...")
        # test_start = time.time()
        # connection.execute_query("RETURN 1")
        # test_time = time.time() - test_start
        # print(f"✅ 连接测试成功，耗时: {test_time:.3f}秒")
        
        # 执行清空操作
        if args.mode == 'all':
            success = optimized_clear_all(connection, args.force)
        else:
            success = optimized_clear_alerts(connection, args.force)
        
        total_time = time.time() - total_start_time
        
        if success:
            print(f"\n🎉 数据库清空完成! 总耗时: {total_time:.3f}秒")
            sys.exit(0)
        else:
            print(f"\n❌ 数据库清空失败! 总耗时: {total_time:.3f}秒")
            sys.exit(1)
            
    except Exception as e:
        total_time = time.time() - total_start_time
        print(f"\n💥 清空过程发生错误: {str(e)}")
        print(f"总耗时: {total_time:.3f}秒")
        sys.exit(1)
    finally:
        try:
            connection.close()
        except:
            pass


if __name__ == "__main__":
    main() 