"""
API依赖项

定义FastAPI路由的依赖注入项
"""

from fastapi import Depends
from app.database.connection import Neo4jConnection, neo4j_connection
from app.services import AlertService, VerdictService, EntityService


def get_neo4j_connection() -> Neo4jConnection:
    """获取Neo4j连接实例"""
    return neo4j_connection


def get_alert_service(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
) -> AlertService:
    """获取告警服务实例"""
    return AlertService(connection)


def get_verdict_service(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
) -> VerdictService:
    """获取研判服务实例"""
    return VerdictService(connection)


def get_entity_service(
    connection: Neo4jConnection = Depends(get_neo4j_connection)
) -> EntityService:
    """获取实体服务实例"""
    return EntityService(connection) 