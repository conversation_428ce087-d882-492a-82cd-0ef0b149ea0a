"""
AlertGraph 应用入口点

运行图数据库管理与API服务
"""

import uvicorn
from app.core.config import settings


def main():
    """启动应用服务器"""
    print(f"启动 {settings.app_name} v{settings.app_version}")
    print(f"Neo4j连接: {settings.neo4j_uri}")
    print(f"API文档: http://localhost:8000{settings.docs_url}")
    
    # 启动FastAPI应用
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
